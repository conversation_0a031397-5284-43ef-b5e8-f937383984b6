// Jest 测试设置文件
import { join } from 'path';
import { existsSync, unlinkSync } from 'fs';

// 设置测试环境变量
(process.env as any).NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // 在测试中减少日志输出

// 测试数据库路径
const TEST_DB_PATH = join(process.cwd(), 'test.db');

// 在每个测试套件开始前清理测试数据库
beforeEach(() => {
  // 清理测试数据库文件
  if (existsSync(TEST_DB_PATH)) {
    try {
      unlinkSync(TEST_DB_PATH);
    } catch (error) {
      // 忽略删除失败的错误
    }
  }
});

// 在所有测试完成后清理
afterAll(() => {
  // 清理测试数据库文件
  if (existsSync(TEST_DB_PATH)) {
    try {
      unlinkSync(TEST_DB_PATH);
    } catch (error) {
      // 忽略删除失败的错误
    }
  }
});

// 全局测试工具函数
global.testUtils = {
  // 创建临时文件路径
  createTempPath: (filename: string) => {
    return join(process.cwd(), '__tests__', 'temp', filename);
  },
  
  // 等待指定时间
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 模拟控制台输出
  mockConsole: () => {
    const originalConsole = { ...console };
    const mockLog = jest.fn();
    const mockError = jest.fn();
    const mockWarn = jest.fn();
    
    console.log = mockLog;
    console.error = mockError;
    console.warn = mockWarn;
    
    return {
      mockLog,
      mockError,
      mockWarn,
      restore: () => {
        console.log = originalConsole.log;
        console.error = originalConsole.error;
        console.warn = originalConsole.warn;
      },
    };
  },
};

// 扩展全局类型定义
declare global {
  var testUtils: {
    createTempPath: (filename: string) => string;
    delay: (ms: number) => Promise<void>;
    mockConsole: () => {
      mockLog: jest.Mock;
      mockError: jest.Mock;
      mockWarn: jest.Mock;
      restore: () => void;
    };
  };
}
