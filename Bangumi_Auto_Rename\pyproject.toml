[project]
name = "<PERSON>umi_Auto_Rename"
version = "0.2.1"
description = "Default template for PDM package"
authors = [{ name = "KimigaiiWuyi", email = "<EMAIL>" }]
dependencies = [
    "tmdbsimple>=2.9.1",
    "jikanpy-v4>=1.0.2",
    "nicegui>=2.9.1",
    "pywin32>=308",
    "structlog>=24.4.0",
    "frozenlist>=1.5.0",
    "openai>=1.54.0",
    "hachoir>=3.3.0",
    "google>=3.0.0",
    "google-genai>=0.8.0",
]
requires-python = ">=3.9,<4.0"
readme = "README.md"
license = { text = "GPLv3" }


[tool.pdm]
distribution = false

[tool.pdm.scripts]
start = "src.start"

[[tool.pdm.source]]
name = "TUNA"
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"

[tool.isort]
profile = "black"
line_length = 79
length_sort = true
skip_gitignore = true
force_sort_within_sections = true
extra_standard_library = ["typing_extensions"]
