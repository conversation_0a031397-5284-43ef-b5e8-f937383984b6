{"name": "seiri-chan", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "verify:tmdb": "tsx scripts/verify-tmdb-integration.ts"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@remotion/media-parser": "^4.0.331", "next": "15.4.5", "next-intl": "^4.3.4", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "smol-toml": "^1.4.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "swr": "^2.3.4", "tmdb-ts": "^2.0.1", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^30.0.5", "jest-environment-node": "^30.0.5", "pino": "^9.7.0", "pino-pretty": "^13.1.1", "tailwindcss": "^4", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "<5.9.0", "uuid": "^11.1.0"}}