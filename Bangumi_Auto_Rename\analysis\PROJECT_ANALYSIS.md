# Bangumi Auto Rename 项目分析文档

## 1. 项目概述

`Bangumi Auto Rename` 是一个旨在自动化整理本地媒体文件的工具，特别针对动漫、电视剧和电影。它能将下载的、命名混乱的媒体文件，根据TMDB（The Movie Database）的数据，重命名并整理成Emby、Jellyfin、Plex等媒体服务器偏好的标准目录结构。

项目的核心优势在于其高度的自动化能力和精准的识别率。它不仅支持与qBittorrent等下载工具的无缝集成，实现下载后自动处理，还创新性地引入了**AI辅助识别**功能，以解决传统基于规则的重命名方法难以处理的复杂情况（如BD资源的多季合集、特典与正片的混合发布等）。

## 2. 整体架构

本项目采用Python语言开发，后端基于FastAPI，并使用NiceGUI构建了一个简洁易用的Web用户界面。其架构可以分为以下几个核心层次：

*   **表示层 (Web UI)**:
    *   使用 **NiceGUI** 框架构建，为用户提供了可视化的操作界面。
    *   用户可以通过网页完成所有操作，包括：添加手动处理任务、配置系统参数（API密钥、媒体库路径）、查看处理历史和日志。
    *   位于 `src/web.py`, `src/main_page.py` 和 `src/pages/` 目录下。

*   **服务层 (Web Service)**:
    *   基于 **FastAPI**（NiceGUI的底层框架）。
    *   提供了一个核心的API端点 `POST /sendTask`，用于接收来自qBittorrent等外部应用的自动化处理请求。
    *   负责解析传入的请求参数，并启动核心处理流程。
    *   位于 `src/web.py`。

*   **业务逻辑层 (Core Logic)**:
    *   这是整个应用的大脑，负责执行实际的重命名和整理任务。
    *   包含**传统处理引擎**和**AI处理引擎**两个分支。
    *   负责文件名清洗、媒体信息检索、季度与集数识别、文件映射关系生成等。
    *   主要位于 `src/rename/` 目录下，尤其是 `process.py`。

*   **数据与配置层 (Data & Config)**:
    *   **信息获取 (`get_info.py`)**: 封装了与TMDB和MyAnimeList API的交互逻辑，负责抓取媒体元数据。
    *   **AI客户端 (`ai/client.py`)**: 封装了与大语言模型（OpenAI兼容API）的交互，负责发送请求和解析结果。
    *   **配置管理 (`config/config_manager.py`)**: 负责配置文件的读写和管理，为整个应用提供统一的配置访问接口。
    *   **文件传输 (`rename/trans.py`)**: 负责执行最终的文件操作（硬链接、复制、移动）。

*   **支撑模块 (Utilities)**:
    *   **日志 (`logger.py`)**: 提供日志记录功能。
    *   **数据模型 (`models.py`, `ai/models.py`)**: 使用Pydantic定义了清晰的数据结构，如任务模型和AI分析结果模型。
    *   **工具函数 (`utils/`, `rename/cleaner.py`)**: 提供了各种辅助功能，如路径处理、字符串清洗、文件名解析等。

接下来，我们将详细分析每个组件的功能。

## 3. 组件详细分析

### 3.1. Web UI 与服务层 (`src/web.py`, `src/main_page.py`, `src/pages/config_page.py`)

Web层是用户与系统交互的入口，同时也是自动化流程的接入点。

*   **UI构建**: 使用`NiceGUI`构建，代码集中在[`src/main_page.py`](src/main_page.py)和[`src/pages/`](src/pages)目录。主页面([`main_page.py`](src/main_page.py:13))定义了整体布局，包括顶栏、功能按钮和内容展示区。各个子页面（如添加任务、配置页）被模块化到[`src/pages/`](src/pages)中，使得UI逻辑清晰且易于维护。
*   **自动化接口**: [`src/web.py`](src/web.py:24)中定义的`POST /sendTask`接口是实现自动化的关键。它遵循了良好的API设计实践，通过接收`path`和`tag`参数来触发后端处理。这种设计将Web服务与核心逻辑解耦，使得核心处理模块可以独立于Web界面进行测试和调用。
*   **启动流程**: 项目通过[`src/start.py`](src/start.py)启动，该文件配置并运行`NiceGUI`应用，监听在`5999`端口。
*   **配置页面新增AI测试功能**: 在配置页（`src/pages/config_page.py`）中，新增“🧪 测试AI识别功能”和“⚙️ 测试OpenAI API功能”按钮。点击后调用`src/ai/unified_ai_tester.py`中的`UnifiedAITester`，基于当前界面配置进行异步测试，支持AI识别流程测试及OpenAI API多种输出格式测试（`function_calling`、`json_object`、`structured_output`、`text`），并将结果反馈至UI。

### 3.2. 核心处理流程 (`src/rename/process.py`)

这是整个应用最核心的部分，`Rename`类封装了所有处理逻辑。

1.  **入口与分发 (`process`方法)**: 接收一个路径后，首先判断路径类型（文件/文件夹），并智能决定是处理单个媒体集还是遍历处理多个子目录，最终将任务分发给私有的`_process`方法。

2.  **文件名预处理**: 在`_process`方法中，首先调用[`src/rename/cleaner.py`](src/rename/cleaner.py)中的一系列函数（如`remove_tag`, `remove_season`）对文件名进行清洗，提取出最干净、最适合用于搜索的番剧或电影名称。

3.  **媒体类型智能判断**:
    *   系统会用清洗后的名称同时向TMDB搜索**电视剧**和**电影**。
    *   通过一个内部评分机制，综合考虑“是否搜到电视剧”、“是否搜到电影”、“文件名是否含季度信息”、“文件夹内文件数量”等多个因素，以极高的准确率判断出媒体的真实类型。这是传统模式成功的关键。

4.  **元数据获取 (`src/rename/get_info.py`)**:
    *   根据判断出的类型，调用`Search`类的方法（如`get_tv_info_with_seasons`）从TMDB获取详细信息。
    *   该模块包含了完整的季度和剧集信息获取逻辑，为后续的精确匹配和AI分析提供了坚实的数据基础。
    *   当TMDB搜索动漫失败时，会自动切换到`jikanpy`库，从MyAnimeList获取数据作为备用，增强了识别的鲁棒性。

5.  **文件映射与重命名**:
    *   **传统模式**: 对于电视剧，通过`get_season_id`方法确定季度。然后，在`process_sub`方法中，通过正则表达式匹配文件名中的集数、特典关键词（`SP`, `OP`等），生成目标文件路径，并存入一个映射字典中。
    *   **AI模式**: 如果启用了AI，则调用AI模块进行分析，AI的结果将用于修正或完全替代传统模式生成的映射关系。

6.  **执行与收尾**: 所有文件处理操作（硬链接/复制/移动）由[`src/rename/trans.py`](src/rename/trans.py)模块根据最终的映射字典执行。最后，任务结果被序列化为JSON文件，用于前端展示。

### 3.3. AI增强模块 (`src/ai/`)

AI模块是本项目的一大亮点，它解决了传统规则难以覆盖的灰色地带。

*   **AI客户端工厂 (`client.py`)**:
    *   根据配置（`ai_provider`）选择具体客户端 (`OpenAIClient` 或 `GeminiClient`)，统一管理是否启用 (`ai_enabled`) 及置信度阈值 (`ai_confidence_threshold`)。
*   **OpenAI客户端 (`openai_client.py`)**:
    *   支持多种输出格式 (`function_calling`、`json_object`、`structured_output`、`text`)，通过配置项 `openai_output_format` 动态设定请求参数。
    *   使用通用系统提示词 (`AIClient.get_system_prompt`) 与基础提示 (`AIClient.build_common_prompt`) 结合 OpenAI 特定的 JSON 格式要求，保证严格格式化输出。
    *   包含丰富的 JSON 提取与清洗逻辑 (`_extract_and_validate_json`、`_extract_json_from_response`、`_clean_json_content`)，兼容函数调用模式和纯文本响应，并使用 Pydantic 验证结果。
*   **Gemini客户端 (`gemini_client.py`)**:
    *   实现与 Gemini API 的交互接口，与 OpenAI 客户端保持一致，便于提供商切换与扩展。
*   **统一AI测试器 (`unified_ai_tester.py`)**:
    *   使用当前界面配置进行测试，不修改持久配置，加载项目内测试用例并与期望结果对比。
    *   支持 AI 识别功能测试和 OpenAI API 格式兼容性测试，异步执行并生成详细的匹配度报告。

*   **视频分析器 (`video_analyzer.py`)**: 在调用AI前，此模块会使用`ffprobe`分析视频文件的时长等元数据，为AI提供更丰富的判断依据。

*   **AI处理器 (`rename/ai_processor.py`)**:
    *   作为AI功能与主流程的桥梁，`AIProcessor`负责调用AI客户端，并根据用户设置的置信度阈值决定是否采纳AI的分析结果。
    *   `apply_ai_mapping`方法将AI返回的映射关系应用到最终的重命名计划中，实现了对传统流程的智能优化。

### 3.4. 配置管理 (`src/config/config_manager.py`)

一个健壮的配置管理器是保证应用稳定性和可维护性的基石。

*   **全局单例**: `cm`对象作为全局唯一的配置实例，确保了配置的一致性。
*   **自动迁移与默认值**: `ConfigManager`在初始化时会自动检查配置文件，为缺失的配置项添加默认值，并移除废弃的旧配置项，极大地简化了应用的更新和维护。
*   **安全写入**: 采用“备份-删除-重命名”的原子化写入操作，有效防止了配置文件因意外中断而损坏。

## 4. 总结

`Bangumi Auto Rename`是一个设计精良、功能强大的媒体整理工具。它的架构清晰，模块化程度高。项目巧妙地结合了**高效的传统规则引擎**和**强大的AI分析引擎**：前者保证了对常规任务的处理速度和效率，后者则作为“专家系统”解决了传统方法难以应对的复杂和模糊匹配问题。

这种“传统+AI”的双引擎设计模式，是本项目最值得称道的亮点，为其他自动化工具的设计提供了宝贵的参考。