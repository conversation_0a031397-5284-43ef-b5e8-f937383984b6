# TMDB API 使用示例

## 简化后的搜索API

### 基本搜索
```typescript
import { getTmdbClient } from '@/lib/tmdb';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
const tmdbClient = getTmdbClient(prisma);

// 搜索媒体（默认最多3页）
const results = await tmdbClient.searchMulti('The Matrix');

// 直接遍历结果数组
for (const item of results) {
  if (item.media_type === 'movie') {
    console.log(`电影: ${item.title} (${item.release_date})`);
  } else if (item.media_type === 'tv') {
    console.log(`电视剧: ${item.name} (${item.first_air_date})`);
  }
}
```

### 自定义最大页数
```typescript
// 搜索更多结果（最多5页）
const moreResults = await tmdbClient.searchMulti('Marvel', 5);

// 只搜索第一页
const firstPageOnly = await tmdbClient.searchMulti('Batman', 1);
```

### 跳过缓存
```typescript
// 获取最新数据，跳过缓存
const freshResults = await tmdbClient.searchMulti('Spider-Man', 3, true);
```

## 类型安全的结果处理

### 电影结果处理
```typescript
import type { TmdbSearchResultItem } from '@/lib/tmdb/types';

const results = await tmdbClient.searchMulti('Inception');

// 过滤电影结果
const movies = results.filter((item): item is MovieWithMediaType => 
  item.media_type === 'movie'
);

for (const movie of movies) {
  console.log({
    id: movie.id,
    title: movie.title,
    originalTitle: movie.original_title,
    releaseDate: movie.release_date,
    overview: movie.overview,
    voteAverage: movie.vote_average,
    posterPath: movie.poster_path,
  });
}
```

### 电视剧结果处理
```typescript
const results = await tmdbClient.searchMulti('Breaking Bad');

// 过滤电视剧结果
const tvShows = results.filter((item): item is TVWithMediaType => 
  item.media_type === 'tv'
);

for (const show of tvShows) {
  console.log({
    id: show.id,
    name: show.name,
    originalName: show.original_name,
    firstAirDate: show.first_air_date,
    overview: show.overview,
    voteAverage: show.vote_average,
    posterPath: show.poster_path,
    originCountry: show.origin_country,
  });
}
```

## 获取详细信息

### 获取电影详情
```typescript
// 从搜索结果中找到目标电影
const results = await tmdbClient.searchMulti('The Matrix');
const movie = results.find(item => 
  item.media_type === 'movie' && item.id === 603
);

if (movie) {
  // 获取完整的电影详情
  const movieDetails = await tmdbClient.getMovieDetails(movie.id);
  
  console.log({
    title: movieDetails.title,
    runtime: movieDetails.runtime,
    budget: movieDetails.budget,
    revenue: movieDetails.revenue,
    genres: movieDetails.genres,
    productionCompanies: movieDetails.production_companies,
  });
}
```

### 获取电视剧详情和季信息
```typescript
// 从搜索结果中找到目标电视剧
const results = await tmdbClient.searchMulti('Breaking Bad');
const tvShow = results.find(item => 
  item.media_type === 'tv' && item.id === 1396
);

if (tvShow) {
  // 获取完整的电视剧详情
  const tvDetails = await tmdbClient.getTvDetails(tvShow.id);
  
  console.log({
    name: tvDetails.name,
    numberOfSeasons: tvDetails.number_of_seasons,
    numberOfEpisodes: tvDetails.number_of_episodes,
    status: tvDetails.status,
    networks: tvDetails.networks,
  });
  
  // 获取第一季详情
  const seasonDetails = await tmdbClient.getSeasonDetails(tvShow.id, 1);
  
  console.log({
    seasonName: seasonDetails.name,
    episodeCount: seasonDetails.episodes.length,
    airDate: seasonDetails.air_date,
  });
}
```

## 错误处理

### 基本错误处理
```typescript
import { TmdbError } from '@/lib/tmdb/types';

try {
  const results = await tmdbClient.searchMulti('Some Movie');
  
  if (results.length === 0) {
    console.log('没有找到匹配的结果');
    return;
  }
  
  // 处理结果...
} catch (error) {
  if (error instanceof TmdbError) {
    console.error(`TMDB API 错误: ${error.message} (代码: ${error.code})`);
  } else {
    console.error('未知错误:', error);
  }
}
```

### 网络错误处理
```typescript
try {
  const results = await tmdbClient.searchMulti('Movie Title');
  // 处理结果...
} catch (error) {
  if (error instanceof TmdbError) {
    switch (error.code) {
      case 'SEARCH_ERROR':
        console.error('搜索失败，请检查网络连接');
        break;
      case 'MOVIE_DETAILS_ERROR':
        console.error('获取电影详情失败');
        break;
      case 'TV_DETAILS_ERROR':
        console.error('获取电视剧详情失败');
        break;
      default:
        console.error(`TMDB 错误: ${error.message}`);
    }
  }
}
```

## 性能优化建议

### 1. 合理使用缓存
```typescript
// 第一次搜索会调用API并缓存结果
const results1 = await tmdbClient.searchMulti('Popular Movie');

// 第二次搜索会使用缓存，速度更快
const results2 = await tmdbClient.searchMulti('Popular Movie');

// 需要最新数据时跳过缓存
const freshResults = await tmdbClient.searchMulti('Popular Movie', 3, true);
```

### 2. 控制搜索页数
```typescript
// 对于快速预览，只搜索第一页
const quickResults = await tmdbClient.searchMulti('Query', 1);

// 对于详细搜索，可以增加页数
const detailedResults = await tmdbClient.searchMulti('Query', 5);
```

### 3. 结果过滤
```typescript
const results = await tmdbClient.searchMulti('Action');

// 只处理电影结果，提高性能
const movies = results
  .filter(item => item.media_type === 'movie')
  .slice(0, 10); // 只取前10个结果
```
