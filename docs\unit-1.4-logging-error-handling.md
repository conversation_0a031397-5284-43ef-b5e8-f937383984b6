# 单元 1.4: 日志与错误处理模块 - 完成文档

## 概述
本单元完成了 Seiri-chan 项目的日志系统和错误处理机制，建立了统一的日志记录、错误分类、错误处理和工具函数体系。

## 完成的工作

### 1. 日志系统 (`src/lib/logger/index.ts`)
基于 `pino` 构建了完整的日志系统：

#### 核心日志器
- **主日志器**: 根据环境自动配置格式（开发环境使用 pretty 格式，生产环境使用 JSON 格式）
- **文件日志器**: 支持创建指定文件名的日志器，用于持久化日志
- **专用日志器**:
  - `taskLogger`: 任务处理专用日志
  - `errorLogger`: 错误专用日志
  - `apiLogger`: API 请求专用日志

#### 日志工具函数 (`logUtils`)
- `logTaskStart`: 记录任务开始
- `logTaskComplete`: 记录任务完成
- `logTaskError`: 记录任务失败
- `logFileOperation`: 记录文件操作
- `logApiRequest`: 记录 API 请求
- `logApiError`: 记录 API 错误

#### 日志特性
- 自动时间戳和格式化
- 结构化日志记录
- 开发环境彩色输出
- 生产环境 JSON 格式
- 自动创建日志目录

### 2. 错误处理系统 (`src/lib/errors/index.ts`)
建立了完整的错误分类和处理机制：

#### 错误代码枚举 (`ErrorCode`)
- **通用错误**: `UNKNOWN_ERROR`, `VALIDATION_ERROR`
- **配置错误**: `CONFIG_LOAD_ERROR`, `CONFIG_SAVE_ERROR`, `CONFIG_VALIDATION_ERROR`
- **文件系统错误**: `FILE_NOT_FOUND`, `FILE_ACCESS_DENIED`, `DISK_SPACE_INSUFFICIENT`, `FILE_OPERATION_FAILED`
- **任务错误**: `TASK_NOT_FOUND`, `TASK_ALREADY_RUNNING`, `TASK_EXECUTION_FAILED`
- **网络错误**: `NETWORK_ERROR`, `API_ERROR`, `TMDB_API_ERROR`, `AI_API_ERROR`
- **数据库错误**: `DATABASE_ERROR`, `DATABASE_CONNECTION_ERROR`
- **媒体分析错误**: `MEDIA_ANALYSIS_FAILED`, `MEDIA_NOT_FOUND`, `INVALID_MEDIA_FORMAT`

#### 错误严重级别 (`ErrorSeverity`)
- `LOW`: 低级别错误
- `MEDIUM`: 中等级别错误
- `HIGH`: 高级别错误
- `CRITICAL`: 严重错误

#### 错误类层次结构
- **基础类**: `AppError` - 所有应用错误的基类
- **专用错误类**:
  - `ConfigError`: 配置相关错误
  - `FileOperationError`: 文件操作错误
  - `TaskError`: 任务执行错误
  - `NetworkError`: 网络/API 错误
  - `DatabaseError`: 数据库错误
  - `MediaAnalysisError`: 媒体分析错误

#### 错误工具函数 (`errorUtils`)
- `createErrorResponse`: 创建标准化错误响应
- `logError`: 记录错误日志
- `wrapAsync`: 包装异步函数，自动处理错误
- `fromNativeError`: 从原生错误创建 AppError
- `isErrorCode`: 检查错误是否为特定类型
- `isErrorSeverity`: 检查错误严重级别

#### API 错误处理中间件
- `withErrorHandling`: Next.js API 路由错误处理中间件
- 自动捕获和处理 API 错误
- 标准化错误响应格式
- 自动错误日志记录

### 3. 通用工具模块 (`src/lib/utils/index.ts`)
整合了日志、错误处理和其他通用工具：

#### 应用工具集合 (`appUtils`)
- **核心工具**: 日志器、错误处理、配置管理
- **异步工具**: `delay`, `retry` - 延迟执行和重试机制
- **数据处理**: `safeJsonParse`, `safeJsonStringify` - 安全的 JSON 操作
- **格式化工具**: `formatFileSize`, `formatDuration` - 文件大小和时长格式化
- **实用工具**: `generateId`, `deepClone` - ID 生成和深度克隆
- **性能工具**: `debounce`, `throttle` - 防抖和节流函数
- **文件工具**: `isValidPath`, `normalizePath`, `getFileExtension` - 路径和文件处理
- **媒体工具**: `isVideoFile`, `isSubtitleFile` - 媒体文件类型检查

### 4. Jest 测试系统
建立了完整的测试框架：
- **测试框架**: Jest + ts-jest + @types/jest
- **测试配置**: 完整的 Jest 配置文件和测试设置
- **测试覆盖**: 33 个测试用例全部通过
  - 配置管理器测试: 12 个测试用例
  - 错误处理测试: 21 个测试用例
- **测试脚本**: test, test:watch, test:coverage, test:ci
- **覆盖率**: 57.14% 总体覆盖率，核心模块达到 66-100%

## 技术特性

### 结构化日志
- 使用 JSON 格式的结构化日志
- 支持上下文信息和元数据
- 自动时间戳和请求 ID 跟踪
- 分级日志记录（debug, info, warn, error, fatal）

### 类型安全错误处理
- 强类型错误代码和严重级别
- 继承式错误类设计
- 自动错误序列化和反序列化
- 上下文信息保留

### 统一错误响应
- 标准化的 API 错误响应格式
- 自动错误日志记录
- 请求 ID 跟踪
- 错误严重级别映射到 HTTP 状态码

### 开发体验优化
- 开发环境友好的日志格式
- 详细的错误堆栈跟踪
- 自动错误分类和处理
- 丰富的工具函数集合

## 使用示例

### 日志记录
```typescript
import { logger, logUtils } from '@/lib/utils';

// 基础日志
logger.info('任务开始处理', { taskId: 'task-123' });

// 专用日志工具
logUtils.logTaskStart('task-123', 'anime', '/path/to/source');
logUtils.logFileOperation('task-123', 'copy', 'source.mkv', 'target.mkv', true);
```

### 错误处理
```typescript
import { AppError, ErrorCode, ErrorSeverity, errorUtils } from '@/lib/utils';

// 创建应用错误
throw new AppError(
  '文件不存在',
  ErrorCode.FILE_NOT_FOUND,
  ErrorSeverity.MEDIUM,
  { filePath: '/path/to/file' }
);

// 包装异步函数
const safeFunction = errorUtils.wrapAsync(
  riskyAsyncFunction,
  ErrorCode.TASK_EXECUTION_FAILED
);
```

### API 错误处理
```typescript
import { withErrorHandling } from '@/lib/utils';

export default withErrorHandling(async (req, res) => {
  // API 逻辑
  // 错误会被自动捕获和处理
});
```

## 下一步
- 阶段二: 核心后端逻辑 (Core Backend - Traditional Engine)
- 单元 2.1: TMDB 客户端与缓存

## 文件位置
- 日志模块: `src/lib/logger/index.ts`
- 错误处理: `src/lib/errors/index.ts`
- 通用工具: `src/lib/utils/index.ts`
- 测试配置: `jest.config.js`
- 测试设置: `__tests__/setup.ts`
- 配置管理测试: `__tests__/lib/config/manager.test.ts`
- 错误处理测试: `__tests__/lib/errors/index.test.ts`
- 测试文档: `docs/testing-setup.md`
- 日志目录: `logs/` (运行时创建)
