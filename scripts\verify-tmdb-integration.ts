#!/usr/bin/env tsx
/**
 * TMDB 集成验证脚本
 * 
 * 此脚本验证 TMDB 客户端与配置管理的集成是否正确工作
 * 包括：配置加载、API 连接、搜索、获取详情等功能
 */

import { PrismaClient } from '@prisma/client';
import { getTmdbClient } from '../src/lib/tmdb';
import { getConfig } from '../src/lib/config';
import path from 'path';

// 测试用例数据
const TEST_CASES = {
  // 知名电视剧 - Breaking Bad
  tvShow: {
    searchQuery: 'Breaking Bad',
    expectedTmdbId: 1396,
    expectedName: 'Breaking Bad',
    testSeason: 1,
  },
  // 知名电影 - The Matrix
  movie: {
    searchQuery: 'The Matrix',
    expectedTmdbId: 603,
    expectedTitle: 'The Matrix',
  },
  // 知名动画 - Attack on Titan
  anime: {
    searchQuery: 'Attack on Titan',
    expectedTmdbId: 1429,
    expectedName: 'Attack on Titan',
    testSeason: 1,
  },
};

async function main() {
  console.log('🚀 开始 TMDB 集成验证...\n');

  const prisma = new PrismaClient();

  try {
    // 1. 验证配置加载
    console.log('📋 1. 验证配置加载...');

    let config;
    try {
      config = getConfig();
    } catch (error) {
      console.error('   ❌ 配置加载失败:', error);
      throw new Error('无法加载配置文件，请确保 seiri.toml 文件存在且格式正确');
    }

    if (!config.general.tmdb.accessToken) {
      console.error('   ❌ TMDB API Key 未配置');
      console.error('   💡 请在 seiri.toml 文件中配置 [general.tmdb] 部分的 accessToken');
      throw new Error('TMDB API Key 未配置');
    }
    
    console.log(`   ✅ 配置加载成功`);
    console.log(`   📍 语言: ${config.general.tmdb.language}`);
    console.log(`   🌍 区域: ${config.general.tmdb.region}`);
    console.log(`   🔑 API Key: ${config.general.tmdb.accessToken.substring(0, 20)}...`);
    console.log();

    // 2. 初始化 TMDB 客户端
    console.log('🔧 2. 初始化 TMDB 客户端...');
    const tmdbClient = getTmdbClient(prisma);
    console.log('   ✅ TMDB 客户端初始化成功');
    console.log();

    // 3. 测试连接
    console.log('🔗 3. 测试 TMDB 连接...');
    const connectionTest = await tmdbClient.testConnection();
    
    if (!connectionTest.success) {
      throw new Error(`TMDB 连接失败: ${connectionTest.message}`);
    }
    
    console.log(`   ✅ ${connectionTest.message}`);
    console.log();

    // 4. 测试电视剧搜索和详情获取
    console.log('📺 4. 测试电视剧功能...');
    await testTvShow(tmdbClient);
    console.log();

    // 5. 测试电影搜索和详情获取
    console.log('🎬 5. 测试电影功能...');
    await testMovie(tmdbClient);
    console.log();

    // 6. 测试动画搜索和季详情获取
    console.log('🎌 6. 测试动画功能...');
    await testAnime(tmdbClient);
    console.log();

    // 7. 测试缓存功能
    console.log('💾 7. 测试缓存功能...');
    await testCaching(tmdbClient);
    console.log();

    console.log('🎉 所有测试通过！TMDB 集成验证成功！');

  } catch (error) {
    console.error('❌ 验证失败:', error);
    await prisma.$disconnect();
    // 使用 setTimeout 来避免日志系统的退出问题
    setTimeout(() => process.exit(1), 100);
    return;
  } finally {
    await prisma.$disconnect();
  }

  // 正常退出也使用 setTimeout
  setTimeout(() => process.exit(0), 100);
}

async function testTvShow(tmdbClient: any) {
  const { searchQuery, expectedTmdbId, expectedName, testSeason } = TEST_CASES.tvShow;

  // 搜索电视剧
  console.log(`   🔍 搜索: "${searchQuery}"`);
  const searchResults = await tmdbClient.searchMulti(searchQuery);

  if (!searchResults || searchResults.length === 0) {
    throw new Error('搜索结果为空');
  }

  const tvResult = searchResults.find(
    (item: any) => item.media_type === 'tv' && item.id === expectedTmdbId
  );

  if (!tvResult) {
    throw new Error(`未找到预期的电视剧 (ID: ${expectedTmdbId})`);
  }

  console.log(`   ✅ 找到电视剧: ${tvResult.name || tvResult.title} (ID: ${tvResult.id})`);

  // 获取电视剧详情
  console.log(`   📖 获取详情: ID ${expectedTmdbId}`);
  const tvDetails = await tmdbClient.getTvDetails(expectedTmdbId);
  
  if (tvDetails.id !== expectedTmdbId) {
    throw new Error('电视剧详情 ID 不匹配');
  }

  console.log(`   ✅ 电视剧详情: ${tvDetails.name}`);
  console.log(`   📅 首播日期: ${tvDetails.first_air_date}`);
  console.log(`   📊 评分: ${tvDetails.vote_average}/10`);
  console.log(`   🎭 季数: ${tvDetails.number_of_seasons}`);

  // 获取季详情
  console.log(`   📚 获取第 ${testSeason} 季详情...`);
  const seasonDetails = await tmdbClient.getSeasonDetails(expectedTmdbId, testSeason);
  
  if (seasonDetails.season_number !== testSeason) {
    throw new Error('季详情季号不匹配');
  }

  console.log(`   ✅ 季详情: ${seasonDetails.name}`);
  console.log(`   📺 集数: ${seasonDetails.episodes.length}`);
  console.log(`   📅 播出日期: ${seasonDetails.air_date}`);
}

async function testMovie(tmdbClient: any) {
  const { searchQuery, expectedTmdbId, expectedTitle } = TEST_CASES.movie;

  // 搜索电影
  console.log(`   🔍 搜索: "${searchQuery}"`);
  const searchResults = await tmdbClient.searchMulti(searchQuery);

  const movieResult = searchResults.find(
    (item: any) => item.media_type === 'movie' && item.id === expectedTmdbId
  );

  if (!movieResult) {
    throw new Error(`未找到预期的电影 (ID: ${expectedTmdbId})`);
  }

  console.log(`   ✅ 找到电影: ${movieResult.title || movieResult.name} (ID: ${movieResult.id})`);

  // 获取电影详情
  console.log(`   📖 获取详情: ID ${expectedTmdbId}`);
  const movieDetails = await tmdbClient.getMovieDetails(expectedTmdbId);
  
  if (movieDetails.id !== expectedTmdbId) {
    throw new Error('电影详情 ID 不匹配');
  }

  console.log(`   ✅ 电影详情: ${movieDetails.title}`);
  console.log(`   📅 上映日期: ${movieDetails.release_date}`);
  console.log(`   📊 评分: ${movieDetails.vote_average}/10`);
  console.log(`   ⏱️ 时长: ${movieDetails.runtime} 分钟`);
}

async function testAnime(tmdbClient: any) {
  const { searchQuery, expectedTmdbId, expectedName, testSeason } = TEST_CASES.anime;

  // 搜索动画
  console.log(`   🔍 搜索: "${searchQuery}"`);
  const searchResults = await tmdbClient.searchMulti(searchQuery);

  const animeResult = searchResults.find(
    (item: any) => item.media_type === 'tv' && item.id === expectedTmdbId
  );

  if (!animeResult) {
    throw new Error(`未找到预期的动画 (ID: ${expectedTmdbId})`);
  }

  console.log(`   ✅ 找到动画: ${animeResult.name || animeResult.title} (ID: ${animeResult.id})`);

  // 获取动画详情和季详情
  const tvDetails = await tmdbClient.getTvDetails(expectedTmdbId);
  const seasonDetails = await tmdbClient.getSeasonDetails(expectedTmdbId, testSeason);

  console.log(`   ✅ 动画详情: ${tvDetails.name}`);
  console.log(`   📚 第 ${testSeason} 季: ${seasonDetails.episodes.length} 集`);
}

async function testCaching(tmdbClient: any) {
  const testQuery = 'Matrix';
  
  // 第一次搜索（应该调用 API）
  console.log(`   🔍 第一次搜索: "${testQuery}" (应该调用 API)`);
  const startTime1 = Date.now();
  await tmdbClient.searchMulti(testQuery);
  const duration1 = Date.now() - startTime1;
  console.log(`   ⏱️ 第一次搜索耗时: ${duration1}ms`);

  // 第二次搜索（应该使用缓存）
  console.log(`   🔍 第二次搜索: "${testQuery}" (应该使用缓存)`);
  const startTime2 = Date.now();
  await tmdbClient.searchMulti(testQuery);
  const duration2 = Date.now() - startTime2;
  console.log(`   ⏱️ 第二次搜索耗时: ${duration2}ms`);

  if (duration2 < duration1) {
    console.log(`   ✅ 缓存工作正常 (加速 ${duration1 - duration2}ms)`);
  } else {
    console.log(`   ⚠️ 缓存可能未生效 (第二次搜索未明显加速)`);
  }

  // 测试缓存清理
  console.log(`   🧹 测试缓存清理...`);
  await tmdbClient.clearSearchCache(TEST_CASES.anime.searchQuery)
  await tmdbClient.clearSearchCache(TEST_CASES.movie.searchQuery)
  await tmdbClient.clearSearchCache(TEST_CASES.tvShow.searchQuery)
  await tmdbClient.clearMediaCache(TEST_CASES.anime.expectedTmdbId)
  await tmdbClient.clearMediaCache(TEST_CASES.movie.expectedTmdbId)
  await tmdbClient.clearMediaCache(TEST_CASES.tvShow.expectedTmdbId)
  await tmdbClient.cleanupCache();
  console.log(`   ✅ 缓存清理完成`);
}

// 运行验证脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as verifyTmdbIntegration };
