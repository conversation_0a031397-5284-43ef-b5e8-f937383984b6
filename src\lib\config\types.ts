// 配置文件类型定义
import { z } from 'zod';
import { AvailableLanguages } from 'tmdb-ts';

// TMDB 支持的语言列表（从 tmdb-ts 导入的类型转换为 Zod 枚举）
const TMDB_LANGUAGES = AvailableLanguages;

// TMDB 语言 Schema
export const TmdbLanguageSchema = z.enum(TMDB_LANGUAGES);
export type TmdbLanguage = z.infer<typeof TmdbLanguageSchema>;

// 文件操作类型
export const FileOperationSchema = z.enum(['hardlink', 'softlink', 'copy', 'move', 'skip']);
export type FileOperation = z.infer<typeof FileOperationSchema>;

// 媒体类型
export const MediaTypeSchema = z.enum(['anime', 'tv', 'movie', 'anime_movie']);
export type MediaType = z.infer<typeof MediaTypeSchema>;

// AI 提供商类型
export const AIProviderSchema = z.enum(['openai', 'gemini', 'claude']);
export type AIProvider = z.infer<typeof AIProviderSchema>;

// 通用配置
export const GeneralConfigSchema = z.object({
  // TMDB 配置
  tmdb: z.object({
    accessToken: z.string().optional(),
    language: TmdbLanguageSchema.default('zh-CN'),
    region: z.string().default('CN'),
  }),
  
  // 路径配置
  paths: z.object({
    // 临时目录（用于移动操作的中间存储）
    tempDir: z.string().optional(),
    // 默认原媒体文件库路径（UI中选择媒体文件路径时的父路径）
    defaultSourceRoot: z.string().optional(),
    // 各媒体类型的输出路径
    output: z.object({
      // 动漫输出路径
      anime: z.string().min(1, '动漫输出路径不能为空'),
      // 电视剧输出路径
      tv: z.string().min(1, '电视剧输出路径不能为空'),
      // 电影输出路径
      movie: z.string().min(1, '电影输出路径不能为空'),
      // 动漫电影输出路径
      anime_movie: z.string().min(1, '动漫电影输出路径不能为空'),
    }),
  }),
  
  // 默认设置
  defaults: z.object({
    // 默认文件操作类型
    fileOperation: FileOperationSchema.default('copy'),
    // 默认媒体类型
    mediaType: MediaTypeSchema.default('anime'),
    // 是否启用 AI 模式
    enableAI: z.boolean().default(false),
  }),
});

// AI 配置
export const AIConfigSchema = z.object({
  // AI 提供商
  provider: AIProviderSchema.default('openai'),
  
  // OpenAI 配置
  openai: z.object({
    apiKey: z.string().optional(),
    model: z.string().default('gpt-4o-mini'),
    temperature: z.number().min(0).max(2).default(0.1),
    maxTokens: z.number().min(1).default(4000),
  }),
  
  // Gemini 配置
  gemini: z.object({
    apiKey: z.string().optional(),
    model: z.string().default('gemini-1.5-flash'),
    temperature: z.number().min(0).max(2).default(0.1),
    maxTokens: z.number().min(1).default(4000),
  }),
  
  // Claude 配置
  claude: z.object({
    apiKey: z.string().optional(),
    baseURL: z.url().optional(),
    model: z.string().default('claude-3-haiku-20240307'),
    temperature: z.number().min(0).max(1).default(0.1),
    maxTokens: z.number().min(1).default(4000),
  }),
  
  // AI 分析配置
  analysis: z.object({
    // 置信度阈值（低于此值需要用户确认）
    confidenceThreshold: z.number().min(0).max(1).default(0.8),
    // 是否启用电影分离功能
    enableMovieSeparation: z.boolean().default(true),
    // 自定义 Prompt（可选）
    customPrompt: z.string().optional(),
  }),
});

// 其他配置（预留）
export const OtherConfigSchema = z.object({
  // 日志配置
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
    enableFileLogging: z.boolean().default(true),
    maxLogFiles: z.number().min(1).default(10),
  }),
  
  // 性能配置
  performance: z.object({
    // 并发文件处理数量
    maxConcurrentFiles: z.number().min(1).default(5),
    // 任务队列最大长度
    maxQueueLength: z.number().min(1).default(100),
  }),
  
  // 实验性功能
  experimental: z.object({
    // 启用字幕整理
    enableSubtitleOrganization: z.boolean().default(false),
    // 启用预告片整理
    enableTrailerOrganization: z.boolean().default(false),
    // 启用字体文件收集
    enableFontCollection: z.boolean().default(false),
  }),
});

// 完整配置 Schema
export const ConfigSchema = z.object({
  general: GeneralConfigSchema,
  ai: AIConfigSchema,
  other: OtherConfigSchema,
});

export type Config = z.infer<typeof ConfigSchema>;
export type GeneralConfig = z.infer<typeof GeneralConfigSchema>;
export type AIConfig = z.infer<typeof AIConfigSchema>;
export type OtherConfig = z.infer<typeof OtherConfigSchema>;

// 默认配置
export const DEFAULT_CONFIG: Config = {
  general: {
    tmdb: {
      accessToken: '',
      language: 'zh-CN' as TmdbLanguage,
      region: 'CN',
    },
    paths: {
      defaultSourceRoot: '',
      output: {
        anime: '',
        tv: '',
        movie: '',
        anime_movie: '',
      },
    },
    defaults: {
      fileOperation: 'copy',
      mediaType: 'anime',
      enableAI: false,
    },
  },
  ai: {
    provider: 'openai',
    openai: {
      model: 'gpt-4o-mini',
      temperature: 0.1,
      maxTokens: 4000,
    },
    gemini: {
      model: 'gemini-1.5-flash',
      temperature: 0.1,
      maxTokens: 4000,
    },
    claude: {
      model: 'claude-3-haiku-20240307',
      temperature: 0.1,
      maxTokens: 4000,
    },
    analysis: {
      confidenceThreshold: 0.8,
      enableMovieSeparation: true,
    },
  },
  other: {
    logging: {
      level: 'info',
      enableFileLogging: true,
      maxLogFiles: 10,
    },
    performance: {
      maxConcurrentFiles: 5,
      maxQueueLength: 100,
    },
    experimental: {
      enableSubtitleOrganization: false,
      enableTrailerOrganization: false,
      enableFontCollection: false,
    },
  },
};
