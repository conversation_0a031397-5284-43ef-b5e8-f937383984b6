# 测试系统设置文档

## 概述
本文档描述了 Seiri-chan 项目的测试系统配置和测试策略。项目使用 Jest 作为主要测试框架，提供完整的单元测试覆盖。

## 测试框架配置

### Jest 配置 (`jest.config.js`)
- **测试环境**: Node.js 环境
- **预设**: `ts-jest` 用于 TypeScript 支持
- **模块路径映射**: 支持 `@/` 别名映射到 `src/` 目录
- **测试文件匹配**: `**/__tests__/**/*.test.ts` 和 `**/__tests__/**/*.test.js`
- **覆盖率收集**: 自动收集 `src/` 目录下的代码覆盖率
- **覆盖率报告**: 支持 text、lcov、html 三种格式

### 测试设置 (`__tests__/setup.ts`)
- **环境变量**: 自动设置测试环境变量
- **数据库清理**: 每个测试前自动清理测试数据库
- **全局工具函数**: 提供测试辅助工具
- **控制台模拟**: 支持模拟控制台输出

## 测试目录结构
```
__tests__/
├── setup.ts                    # 测试设置文件
├── temp/                       # 临时测试文件目录
└── lib/                        # 库模块测试
    ├── config/
    │   └── manager.test.ts      # 配置管理器测试
    └── errors/
        └── index.test.ts        # 错误处理测试
```

## 测试脚本
在 `package.json` 中定义了以下测试脚本：

- `pnpm test`: 运行所有测试
- `pnpm test:watch`: 监视模式运行测试
- `pnpm test:coverage`: 运行测试并生成覆盖率报告
- `pnpm test:ci`: CI 环境下运行测试

## 测试覆盖率

### 当前覆盖率状态
- **总体覆盖率**: 57.14% 语句覆盖率
- **配置模块**: 66% 覆盖率
- **错误处理模块**: 100% 覆盖率
- **日志模块**: 55.88% 覆盖率

### 覆盖率目标
- 核心业务逻辑: 90%+ 覆盖率
- 工具函数: 80%+ 覆盖率
- 配置管理: 90%+ 覆盖率
- 错误处理: 95%+ 覆盖率

## 已实现的测试

### 配置管理器测试 (`manager.test.ts`)
- ✅ 单例模式验证
- ✅ 配置文件加载和解析
- ✅ 配置更新和验证
- ✅ 默认配置创建
- ✅ 配置文件存在性检查
- ✅ 各种配置获取方法

**测试用例数**: 12 个
**覆盖的功能**:
- 配置文件不存在时的默认配置加载
- TOML 格式配置文件的正确解析
- 配置更新的深度合并逻辑
- Zod Schema 验证
- 错误处理和回退机制

### 错误处理测试 (`index.test.ts`)
- ✅ 基础 AppError 类功能
- ✅ 特定错误类型 (ConfigError, FileOperationError, etc.)
- ✅ 错误工具函数 (errorUtils)
- ✅ 异步函数包装
- ✅ API 错误处理中间件

**测试用例数**: 21 个
**覆盖的功能**:
- 错误对象的创建和序列化
- 错误代码和严重级别分类
- 错误响应格式化
- 异步函数错误包装
- API 中间件错误处理

## 测试最佳实践

### 测试文件组织
- 测试文件与源文件保持相同的目录结构
- 使用 `.test.ts` 后缀命名测试文件
- 每个模块都有对应的测试文件

### 测试用例编写
- 使用描述性的测试名称（中文）
- 遵循 AAA 模式：Arrange, Act, Assert
- 每个测试用例只测试一个功能点
- 使用适当的 Jest 匹配器

### 模拟和隔离
- 使用 Jest 的模拟功能隔离外部依赖
- 在测试前后进行适当的清理
- 避免测试之间的相互影响

### 错误测试
- 测试正常情况和异常情况
- 验证错误消息和错误类型
- 测试错误处理的边界条件

## 持续集成

### CI 配置建议
```yaml
# GitHub Actions 示例
- name: Run tests
  run: pnpm test:ci

- name: Upload coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### 测试门禁
- 所有测试必须通过才能合并代码
- 新功能必须包含相应的测试用例
- 覆盖率不能低于当前水平

## 下一步测试计划

### 待添加的测试
1. **日志模块测试**: 完善日志功能的测试覆盖
2. **工具函数测试**: 为 `utils` 模块添加测试
3. **数据库模型测试**: Prisma 模型的测试
4. **API 路由测试**: Next.js API 路由的集成测试

### 测试改进
1. **性能测试**: 添加关键功能的性能测试
2. **集成测试**: 添加模块间的集成测试
3. **端到端测试**: 使用 Playwright 进行 E2E 测试
4. **快照测试**: 为配置文件等添加快照测试

## 测试运行示例

### 运行所有测试
```bash
pnpm test
```

### 运行特定测试文件
```bash
pnpm test config/manager.test.ts
```

### 生成覆盖率报告
```bash
pnpm test:coverage
```

### 监视模式开发
```bash
pnpm test:watch
```

## 总结
测试系统已经成功建立，为项目的稳定性和可维护性提供了坚实的基础。当前已有 33 个测试用例全部通过，覆盖了配置管理和错误处理的核心功能。随着项目的发展，将继续扩展测试覆盖范围，确保代码质量。
