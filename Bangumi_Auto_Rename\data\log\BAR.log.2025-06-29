{"event": "Using proactor: IocpProactor", "timestamp": "2025-06-28 17:35:08", "level": "debug"}
{"event": "[配置] anime_movie_path 选择了 None", "timestamp": "2025-06-28 17:35:40", "level": "info"}
{"event": "Using proactor: IocpProactor", "timestamp": "2025-06-29 01:57:07", "level": "debug"}
{"event": "[配置] 配置已修改为： {'api_key': '844de05e8d21154d1899e21f71c96442', 'bangumi_path': '', 'movie_path': '', 'anime_path': '', 'anime_movie_path': '', 'mode': '链接', 'docker_mnt': '/media', 'ai_api_key': '', 'ai_base_url': 'https://api.openai.com/v1', 'ai_model': 'gpt-4o-mini', 'ai_enabled': False, 'ai_confidence_threshold': 0.7}", "timestamp": "2025-06-29 01:57:27", "level": "info"}
{"event": "[移除标签工具] Little <PERSON> Academia", "timestamp": "2025-06-29 01:59:37", "level": "info"}
{"event": "[测试] 处理后的名称: Little Witch Academia", "timestamp": "2025-06-29 01:59:37", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 01:59:37", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=Little+Witch+Academia&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 01:59:43", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 01:59:43", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/69293?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 01:59:49", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 69293, 'adult': False, 'backdrop_path': '/ra6aGM9kmP0XRnWFg1ML2VzpM3W.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2017-01-09', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 10765, 'name': 'Sci-Fi & Fantasy'}, {'id': 35, 'name': 'Comedy'}, {'id': 10759, 'name': 'Action & Adventure'}, {'id': 10762, 'name': 'Kids'}], 'homepage': 'http://tv.littlewitchacademia.jp', 'in_production': False, 'languages': ['ja'], 'last_air_date': '2017-06-26', 'last_episode_to_air': {'id': 1332735, 'name': 'Changing at the Edge of the World', 'overview': 'Everyone seems powerless to stop the magical missile. But Akko believes otherwise, and her friends the \"New Nine Witches\" follow her lead.', 'vote_average': 8.0, 'vote_count': 4, 'air_date': '2017-06-26', 'episode_number': 25, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 1, 'show_id': 69293, 'still_path': '/3cWB0G62sbNsdnSj2ZWQMCD1FRr.jpg'}, 'name': 'Little Witch Academia', 'next_episode_to_air': None, 'networks': [{'id': 614, 'logo_path': '/hSdroyVthq3CynxTIIY7lnS8w1.png', 'name': 'Tokyo MX', 'origin_country': 'JP'}], 'number_of_episodes': 25, 'number_of_seasons': 1, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': 'リトルウィッチアカデミア', 'overview': 'When she was a little girl, Atsuko \"Akko\" Kagari saw a magic show performed by a witch named Shiny Chariot.  From that day on she wanted to be just like her.  Enrolling at Luna Nova Magical Academy and having no magical background, can she become a witch like her idol Shiny Chariot?', 'popularity': 31.354, 'poster_path': '/93Lz0LwbScZ5bmuqoIngLv1LKNb.jpg', 'production_companies': [{'id': 50908, 'logo_path': '/g96SufLc3lXtaU73tAvEbTbXymY.png', 'name': 'TRIGGER', 'origin_country': 'JP'}, {'id': 882, 'logo_path': '/fRSWWjquvzcHjACbtF53utZFIll.png', 'name': 'TOHO', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2017-01-09', 'episode_count': 25, 'id': 83198, 'name': 'Season 1', 'overview': '', 'poster_path': '/mOIq7yxBAErnEPd0otYlpDTI7rE.jpg', 'season_number': 1, 'vote_average': 7.6}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Ended', 'tagline': '', 'type': 'Scripted', 'vote_average': 7.9, 'vote_count': 135}", "timestamp": "2025-06-29 01:59:49", "level": "debug"}
{"event": "[视频分析] 分析了 59 个视频文件", "timestamp": "2025-06-29 02:00:12", "level": "info"}
{"event": "[移除标签工具] Little Witch Academia", "timestamp": "2025-06-29 03:20:35", "level": "info"}
{"event": "[测试] 处理后的名称: Little Witch Academia", "timestamp": "2025-06-29 03:20:35", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:20:35", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=Little+Witch+Academia&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:20:43", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:20:43", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/69293?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:20:49", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 69293, 'adult': False, 'backdrop_path': '/ra6aGM9kmP0XRnWFg1ML2VzpM3W.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2017-01-09', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 10765, 'name': 'Sci-Fi & Fantasy'}, {'id': 35, 'name': 'Comedy'}, {'id': 10759, 'name': 'Action & Adventure'}, {'id': 10762, 'name': 'Kids'}], 'homepage': 'http://tv.littlewitchacademia.jp', 'in_production': False, 'languages': ['ja'], 'last_air_date': '2017-06-26', 'last_episode_to_air': {'id': 1332735, 'name': 'Changing at the Edge of the World', 'overview': 'Everyone seems powerless to stop the magical missile. But Akko believes otherwise, and her friends the \"New Nine Witches\" follow her lead.', 'vote_average': 8.0, 'vote_count': 4, 'air_date': '2017-06-26', 'episode_number': 25, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 1, 'show_id': 69293, 'still_path': '/3cWB0G62sbNsdnSj2ZWQMCD1FRr.jpg'}, 'name': 'Little Witch Academia', 'next_episode_to_air': None, 'networks': [{'id': 614, 'logo_path': '/hSdroyVthq3CynxTIIY7lnS8w1.png', 'name': 'Tokyo MX', 'origin_country': 'JP'}], 'number_of_episodes': 25, 'number_of_seasons': 1, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': 'リトルウィッチアカデミア', 'overview': 'When she was a little girl, Atsuko \"Akko\" Kagari saw a magic show performed by a witch named Shiny Chariot.  From that day on she wanted to be just like her.  Enrolling at Luna Nova Magical Academy and having no magical background, can she become a witch like her idol Shiny Chariot?', 'popularity': 28.9895, 'poster_path': '/93Lz0LwbScZ5bmuqoIngLv1LKNb.jpg', 'production_companies': [{'id': 50908, 'logo_path': '/g96SufLc3lXtaU73tAvEbTbXymY.png', 'name': 'TRIGGER', 'origin_country': 'JP'}, {'id': 882, 'logo_path': '/fRSWWjquvzcHjACbtF53utZFIll.png', 'name': 'TOHO', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2017-01-09', 'episode_count': 25, 'id': 83198, 'name': 'Season 1', 'overview': '', 'poster_path': '/mOIq7yxBAErnEPd0otYlpDTI7rE.jpg', 'season_number': 1, 'vote_average': 7.6}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Ended', 'tagline': '', 'type': 'Scripted', 'vote_average': 7.9, 'vote_count': 135}", "timestamp": "2025-06-29 03:20:49", "level": "debug"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 03:20:49", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:20:49", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/69293/season/1?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:20:55", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含25集", "timestamp": "2025-06-29 03:20:55", "level": "info"}
{"event": "[季度信息] 电视剧《小魔女学园》详细信息获取完成，共1个季度", "timestamp": "2025-06-29 03:20:55", "level": "info"}
{"event": "[视频分析] 分析了 59 个视频文件", "timestamp": "2025-06-29 03:21:33", "level": "info"}
{"event": "[移除标签工具] One-Punch Man", "timestamp": "2025-06-29 03:33:13", "level": "info"}
{"event": "[测试] 处理后的名称: One-Punch Man", "timestamp": "2025-06-29 03:33:13", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:33:13", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=One-Punch+Man&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:33:21", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:33:21", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:33:29", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 63926, 'adult': False, 'backdrop_path': '/3AXLSxMuqyZt8HyrKKfrcJtkswD.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2015-10-05', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 35, 'name': 'Comedy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': 'https://onepunchman-anime.net/', 'in_production': True, 'languages': ['ja'], 'last_air_date': '2019-07-03', 'last_episode_to_air': {'id': 1732566, 'name': 'Cleaning Up the Disciple’s Mess', 'overview': 'Silverfang and Bomb catch up with Garou while the hero hunter is battling Genos, starting a whirlwind battle that draws in more and more combatants as it marches towards its hair-raising climax!', 'vote_average': 8.2, 'vote_count': 18, 'air_date': '2019-07-03', 'episode_number': 12, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 2, 'show_id': 63926, 'still_path': '/vcZkOnTOa9d5K94DkO604RuRuMU.jpg'}, 'name': 'One-Punch Man', 'next_episode_to_air': None, 'networks': [{'id': 98, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}, {'id': 252, 'logo_path': '/zFZ5KCuvx7K0vP9XgGcidfXjuUd.png', 'name': 'TV Aichi', 'origin_country': 'JP'}, {'id': 824, 'logo_path': '/6foQmoPac7WCDnDuDbuCZklmDuA.png', 'name': 'TV Osaka', 'origin_country': 'JP'}], 'number_of_episodes': 24, 'number_of_seasons': 3, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': 'ワンパンマン', 'overview': \"Saitama is a hero who only became a hero for fun. After three years of “special” training, though, he's become so strong that he's practically invincible. In fact, he's too strong — even his mightiest opponents are taken out with a single punch, and it turns out that being devastatingly powerful is actually kind of a bore. With his passion for being a hero lost along with his hair, yet still faced with new enemies every day, how much longer can he keep it going?\", 'popularity': 47.7579, 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'production_companies': [{'id': 3464, 'logo_path': '/9k0nr75nwnNeT2MHerf1OXJN0hj.png', 'name': 'Madhouse', 'origin_country': 'JP'}, {'id': 11884, 'logo_path': '/tHzHPSKdmLT5zCdqFbcmHzZvRIc.png', 'name': 'J.C.STAFF', 'origin_country': 'JP'}, {'id': 528, 'logo_path': '/fO3Aof3lXQclYpBByYC8aneTXwA.png', 'name': 'Bandai Visual', 'origin_country': 'JP'}, {'id': 4720, 'logo_path': '/7wQgZTA4FCkFMDedBYVZiVE87c7.png', 'name': 'ADK', 'origin_country': 'JP'}, {'id': 8157, 'logo_path': '/pEqcMX1aG3JvtDgfh2wNZJLCcb4.png', 'name': 'jeki', 'origin_country': 'JP'}, {'id': 2918, 'logo_path': '/gyEWUBWwqrm3H5T2hkERD9LxpOq.png', 'name': 'Shueisha', 'origin_country': 'JP'}, {'id': 6683, 'logo_path': '/reCkuk3GEomdx5gQpsm15Zl1sgX.png', 'name': 'Lantis', 'origin_country': 'JP'}, {'id': 159157, 'logo_path': '/fEW0xl7S5t0DyFlsx6ypdFC9Poa.png', 'name': 'Banpresto', 'origin_country': 'JP'}, {'id': 60197, 'logo_path': '/D5B8l99tlFJGoMpYJk1kqxYdey.png', 'name': 'Good Smile Company', 'origin_country': 'JP'}, {'id': 3034, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2015-12-04', 'episode_count': 14, 'id': 74239, 'name': 'Specials', 'overview': '', 'poster_path': '/jYUrrLUgZGj1g9VmYa84osrK0zp.jpg', 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2015-10-05', 'episode_count': 12, 'id': 70631, 'name': 'Season 1', 'overview': '', 'poster_path': '/goJyMNld1sDh9WpUyvBpC2cxT2Q.jpg', 'season_number': 1, 'vote_average': 8.2}, {'air_date': '2019-04-10', 'episode_count': 12, 'id': 116885, 'name': 'Season 2', 'overview': 'An increase in villain activity has the Hero Organization worried that the \"Earthdoom prophecy\" will soon come to pass. In an effort to alleviate the overworked heroes, they turn to villains themselves for help. The decision looks ill-advised as at least one villain is more interested in helping the prophecy along than preventing it.', 'poster_path': '/fRibuDuC5MVn19lbvvnuH941w6d.jpg', 'season_number': 2, 'vote_average': 7.6}, {'air_date': None, 'episode_count': 0, 'id': 305144, 'name': 'Season 3', 'overview': '', 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'season_number': 3, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Returning Series', 'tagline': 'All it takes is one punch.', 'type': 'Scripted', 'vote_average': 8.401, 'vote_count': 3747}", "timestamp": "2025-06-29 03:33:29", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 03:33:29", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:33:29", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/0?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:33:37", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含14集", "timestamp": "2025-06-29 03:33:37", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 03:33:37", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:33:37", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/1?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:33:45", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含12集", "timestamp": "2025-06-29 03:33:45", "level": "info"}
{"event": "[季度信息] 正在获取Season 2的详细信息...", "timestamp": "2025-06-29 03:33:45", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:33:45", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/2?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:33:53", "level": "debug"}
{"event": "[季度信息] 获取Season 2信息成功，包含12集", "timestamp": "2025-06-29 03:33:53", "level": "info"}
{"event": "[季度信息] 正在获取Season 3的详细信息...", "timestamp": "2025-06-29 03:33:53", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:33:53", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/3?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:34:01", "level": "debug"}
{"event": "[季度信息] 获取Season 3信息成功，包含0集", "timestamp": "2025-06-29 03:34:01", "level": "info"}
{"event": "[季度信息] 电视剧《一拳超人》详细信息获取完成，共4个季度", "timestamp": "2025-06-29 03:34:01", "level": "info"}
{"event": "[视频分析] 分析了 60 个视频文件", "timestamp": "2025-06-29 03:34:27", "level": "info"}
{"event": "[移除标签工具] One-Punch Man", "timestamp": "2025-06-29 03:35:56", "level": "info"}
{"event": "[测试] 处理后的名称: One-Punch Man", "timestamp": "2025-06-29 03:35:56", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:35:56", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=One-Punch+Man&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:36:02", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:36:02", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:36:08", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 63926, 'adult': False, 'backdrop_path': '/3AXLSxMuqyZt8HyrKKfrcJtkswD.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2015-10-05', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 35, 'name': 'Comedy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': 'https://onepunchman-anime.net/', 'in_production': True, 'languages': ['ja'], 'last_air_date': '2019-07-03', 'last_episode_to_air': {'id': 1732566, 'name': 'Cleaning Up the Disciple’s Mess', 'overview': 'Silverfang and Bomb catch up with Garou while the hero hunter is battling Genos, starting a whirlwind battle that draws in more and more combatants as it marches towards its hair-raising climax!', 'vote_average': 8.2, 'vote_count': 18, 'air_date': '2019-07-03', 'episode_number': 12, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 2, 'show_id': 63926, 'still_path': '/vcZkOnTOa9d5K94DkO604RuRuMU.jpg'}, 'name': 'One-Punch Man', 'next_episode_to_air': None, 'networks': [{'id': 98, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}, {'id': 252, 'logo_path': '/zFZ5KCuvx7K0vP9XgGcidfXjuUd.png', 'name': 'TV Aichi', 'origin_country': 'JP'}, {'id': 824, 'logo_path': '/6foQmoPac7WCDnDuDbuCZklmDuA.png', 'name': 'TV Osaka', 'origin_country': 'JP'}], 'number_of_episodes': 24, 'number_of_seasons': 3, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': 'ワンパンマン', 'overview': \"Saitama is a hero who only became a hero for fun. After three years of “special” training, though, he's become so strong that he's practically invincible. In fact, he's too strong — even his mightiest opponents are taken out with a single punch, and it turns out that being devastatingly powerful is actually kind of a bore. With his passion for being a hero lost along with his hair, yet still faced with new enemies every day, how much longer can he keep it going?\", 'popularity': 47.7579, 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'production_companies': [{'id': 3464, 'logo_path': '/9k0nr75nwnNeT2MHerf1OXJN0hj.png', 'name': 'Madhouse', 'origin_country': 'JP'}, {'id': 11884, 'logo_path': '/tHzHPSKdmLT5zCdqFbcmHzZvRIc.png', 'name': 'J.C.STAFF', 'origin_country': 'JP'}, {'id': 528, 'logo_path': '/fO3Aof3lXQclYpBByYC8aneTXwA.png', 'name': 'Bandai Visual', 'origin_country': 'JP'}, {'id': 4720, 'logo_path': '/7wQgZTA4FCkFMDedBYVZiVE87c7.png', 'name': 'ADK', 'origin_country': 'JP'}, {'id': 8157, 'logo_path': '/pEqcMX1aG3JvtDgfh2wNZJLCcb4.png', 'name': 'jeki', 'origin_country': 'JP'}, {'id': 2918, 'logo_path': '/gyEWUBWwqrm3H5T2hkERD9LxpOq.png', 'name': 'Shueisha', 'origin_country': 'JP'}, {'id': 6683, 'logo_path': '/reCkuk3GEomdx5gQpsm15Zl1sgX.png', 'name': 'Lantis', 'origin_country': 'JP'}, {'id': 159157, 'logo_path': '/fEW0xl7S5t0DyFlsx6ypdFC9Poa.png', 'name': 'Banpresto', 'origin_country': 'JP'}, {'id': 60197, 'logo_path': '/D5B8l99tlFJGoMpYJk1kqxYdey.png', 'name': 'Good Smile Company', 'origin_country': 'JP'}, {'id': 3034, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2015-12-04', 'episode_count': 14, 'id': 74239, 'name': 'Specials', 'overview': '', 'poster_path': '/jYUrrLUgZGj1g9VmYa84osrK0zp.jpg', 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2015-10-05', 'episode_count': 12, 'id': 70631, 'name': 'Season 1', 'overview': '', 'poster_path': '/goJyMNld1sDh9WpUyvBpC2cxT2Q.jpg', 'season_number': 1, 'vote_average': 8.2}, {'air_date': '2019-04-10', 'episode_count': 12, 'id': 116885, 'name': 'Season 2', 'overview': 'An increase in villain activity has the Hero Organization worried that the \"Earthdoom prophecy\" will soon come to pass. In an effort to alleviate the overworked heroes, they turn to villains themselves for help. The decision looks ill-advised as at least one villain is more interested in helping the prophecy along than preventing it.', 'poster_path': '/fRibuDuC5MVn19lbvvnuH941w6d.jpg', 'season_number': 2, 'vote_average': 7.6}, {'air_date': None, 'episode_count': 0, 'id': 305144, 'name': 'Season 3', 'overview': '', 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'season_number': 3, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Returning Series', 'tagline': 'All it takes is one punch.', 'type': 'Scripted', 'vote_average': 8.401, 'vote_count': 3747}", "timestamp": "2025-06-29 03:36:08", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 03:36:08", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:36:08", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/0?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:36:13", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含14集", "timestamp": "2025-06-29 03:36:13", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 03:36:13", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:36:13", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/1?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:36:19", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含12集", "timestamp": "2025-06-29 03:36:19", "level": "info"}
{"event": "[季度信息] 正在获取Season 2的详细信息...", "timestamp": "2025-06-29 03:36:19", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:36:19", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/2?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:36:25", "level": "debug"}
{"event": "[季度信息] 获取Season 2信息成功，包含12集", "timestamp": "2025-06-29 03:36:25", "level": "info"}
{"event": "[季度信息] 正在获取Season 3的详细信息...", "timestamp": "2025-06-29 03:36:25", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:36:25", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/3?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:36:30", "level": "debug"}
{"event": "[季度信息] 获取Season 3信息成功，包含0集", "timestamp": "2025-06-29 03:36:30", "level": "info"}
{"event": "[季度信息] 电视剧《一拳超人》详细信息获取完成，共4个季度", "timestamp": "2025-06-29 03:36:30", "level": "info"}
{"event": "[视频分析] 分析了 60 个视频文件", "timestamp": "2025-06-29 03:36:57", "level": "info"}
{"event": "[AI识别] JSON结构验证成功，置信度: 0.85", "timestamp": "2025-06-29 03:39:53", "level": "info"}
{"event": "[移除标签工具] One-Punch Man S2", "timestamp": "2025-06-29 03:41:13", "level": "info"}
{"event": "[测试] 处理后的名称: One-Punch Man", "timestamp": "2025-06-29 03:41:13", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:41:13", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=One-Punch+Man&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:41:21", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:41:21", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:41:29", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 63926, 'adult': False, 'backdrop_path': '/3AXLSxMuqyZt8HyrKKfrcJtkswD.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2015-10-05', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 35, 'name': 'Comedy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': 'https://onepunchman-anime.net/', 'in_production': True, 'languages': ['ja'], 'last_air_date': '2019-07-03', 'last_episode_to_air': {'id': 1732566, 'name': 'Cleaning Up the Disciple’s Mess', 'overview': 'Silverfang and Bomb catch up with Garou while the hero hunter is battling Genos, starting a whirlwind battle that draws in more and more combatants as it marches towards its hair-raising climax!', 'vote_average': 8.2, 'vote_count': 18, 'air_date': '2019-07-03', 'episode_number': 12, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 2, 'show_id': 63926, 'still_path': '/vcZkOnTOa9d5K94DkO604RuRuMU.jpg'}, 'name': 'One-Punch Man', 'next_episode_to_air': None, 'networks': [{'id': 98, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}, {'id': 252, 'logo_path': '/zFZ5KCuvx7K0vP9XgGcidfXjuUd.png', 'name': 'TV Aichi', 'origin_country': 'JP'}, {'id': 824, 'logo_path': '/6foQmoPac7WCDnDuDbuCZklmDuA.png', 'name': 'TV Osaka', 'origin_country': 'JP'}], 'number_of_episodes': 24, 'number_of_seasons': 3, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': 'ワンパンマン', 'overview': \"Saitama is a hero who only became a hero for fun. After three years of “special” training, though, he's become so strong that he's practically invincible. In fact, he's too strong — even his mightiest opponents are taken out with a single punch, and it turns out that being devastatingly powerful is actually kind of a bore. With his passion for being a hero lost along with his hair, yet still faced with new enemies every day, how much longer can he keep it going?\", 'popularity': 47.7579, 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'production_companies': [{'id': 3464, 'logo_path': '/9k0nr75nwnNeT2MHerf1OXJN0hj.png', 'name': 'Madhouse', 'origin_country': 'JP'}, {'id': 11884, 'logo_path': '/tHzHPSKdmLT5zCdqFbcmHzZvRIc.png', 'name': 'J.C.STAFF', 'origin_country': 'JP'}, {'id': 528, 'logo_path': '/fO3Aof3lXQclYpBByYC8aneTXwA.png', 'name': 'Bandai Visual', 'origin_country': 'JP'}, {'id': 4720, 'logo_path': '/7wQgZTA4FCkFMDedBYVZiVE87c7.png', 'name': 'ADK', 'origin_country': 'JP'}, {'id': 8157, 'logo_path': '/pEqcMX1aG3JvtDgfh2wNZJLCcb4.png', 'name': 'jeki', 'origin_country': 'JP'}, {'id': 2918, 'logo_path': '/gyEWUBWwqrm3H5T2hkERD9LxpOq.png', 'name': 'Shueisha', 'origin_country': 'JP'}, {'id': 6683, 'logo_path': '/reCkuk3GEomdx5gQpsm15Zl1sgX.png', 'name': 'Lantis', 'origin_country': 'JP'}, {'id': 159157, 'logo_path': '/fEW0xl7S5t0DyFlsx6ypdFC9Poa.png', 'name': 'Banpresto', 'origin_country': 'JP'}, {'id': 60197, 'logo_path': '/D5B8l99tlFJGoMpYJk1kqxYdey.png', 'name': 'Good Smile Company', 'origin_country': 'JP'}, {'id': 3034, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2015-12-04', 'episode_count': 14, 'id': 74239, 'name': 'Specials', 'overview': '', 'poster_path': '/jYUrrLUgZGj1g9VmYa84osrK0zp.jpg', 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2015-10-05', 'episode_count': 12, 'id': 70631, 'name': 'Season 1', 'overview': '', 'poster_path': '/goJyMNld1sDh9WpUyvBpC2cxT2Q.jpg', 'season_number': 1, 'vote_average': 8.2}, {'air_date': '2019-04-10', 'episode_count': 12, 'id': 116885, 'name': 'Season 2', 'overview': 'An increase in villain activity has the Hero Organization worried that the \"Earthdoom prophecy\" will soon come to pass. In an effort to alleviate the overworked heroes, they turn to villains themselves for help. The decision looks ill-advised as at least one villain is more interested in helping the prophecy along than preventing it.', 'poster_path': '/fRibuDuC5MVn19lbvvnuH941w6d.jpg', 'season_number': 2, 'vote_average': 7.6}, {'air_date': None, 'episode_count': 0, 'id': 305144, 'name': 'Season 3', 'overview': '', 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'season_number': 3, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Returning Series', 'tagline': 'All it takes is one punch.', 'type': 'Scripted', 'vote_average': 8.401, 'vote_count': 3747}", "timestamp": "2025-06-29 03:41:29", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 03:41:29", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:41:29", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/0?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:41:37", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含14集", "timestamp": "2025-06-29 03:41:37", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 03:41:37", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:41:37", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/1?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:41:45", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含12集", "timestamp": "2025-06-29 03:41:45", "level": "info"}
{"event": "[季度信息] 正在获取Season 2的详细信息...", "timestamp": "2025-06-29 03:41:45", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:41:45", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/2?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:41:53", "level": "debug"}
{"event": "[季度信息] 获取Season 2信息成功，包含12集", "timestamp": "2025-06-29 03:41:53", "level": "info"}
{"event": "[季度信息] 正在获取Season 3的详细信息...", "timestamp": "2025-06-29 03:41:53", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 03:41:53", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/3?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 03:42:01", "level": "debug"}
{"event": "[季度信息] 获取Season 3信息成功，包含0集", "timestamp": "2025-06-29 03:42:01", "level": "info"}
{"event": "[季度信息] 电视剧《一拳超人》详细信息获取完成，共4个季度", "timestamp": "2025-06-29 03:42:01", "level": "info"}
{"event": "[视频分析] 分析了 50 个视频文件", "timestamp": "2025-06-29 03:42:25", "level": "info"}
{"event": "[移除标签工具] One-Punch Man S2", "timestamp": "2025-06-29 04:06:39", "level": "info"}
{"event": "[测试] 处理后的名称: One-Punch Man", "timestamp": "2025-06-29 04:06:39", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 04:06:39", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=One-Punch+Man&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 04:06:45", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 04:06:45", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 04:06:51", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 63926, 'adult': False, 'backdrop_path': '/3AXLSxMuqyZt8HyrKKfrcJtkswD.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2015-10-05', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 35, 'name': 'Comedy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': 'https://onepunchman-anime.net/', 'in_production': True, 'languages': ['ja'], 'last_air_date': '2019-07-03', 'last_episode_to_air': {'id': 1732566, 'name': 'Cleaning Up the Disciple’s Mess', 'overview': 'Silverfang and Bomb catch up with Garou while the hero hunter is battling Genos, starting a whirlwind battle that draws in more and more combatants as it marches towards its hair-raising climax!', 'vote_average': 8.2, 'vote_count': 18, 'air_date': '2019-07-03', 'episode_number': 12, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 2, 'show_id': 63926, 'still_path': '/vcZkOnTOa9d5K94DkO604RuRuMU.jpg'}, 'name': 'One-Punch Man', 'next_episode_to_air': None, 'networks': [{'id': 98, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}, {'id': 252, 'logo_path': '/zFZ5KCuvx7K0vP9XgGcidfXjuUd.png', 'name': 'TV Aichi', 'origin_country': 'JP'}, {'id': 824, 'logo_path': '/6foQmoPac7WCDnDuDbuCZklmDuA.png', 'name': 'TV Osaka', 'origin_country': 'JP'}], 'number_of_episodes': 24, 'number_of_seasons': 3, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': 'ワンパンマン', 'overview': \"Saitama is a hero who only became a hero for fun. After three years of “special” training, though, he's become so strong that he's practically invincible. In fact, he's too strong — even his mightiest opponents are taken out with a single punch, and it turns out that being devastatingly powerful is actually kind of a bore. With his passion for being a hero lost along with his hair, yet still faced with new enemies every day, how much longer can he keep it going?\", 'popularity': 47.7579, 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'production_companies': [{'id': 3464, 'logo_path': '/9k0nr75nwnNeT2MHerf1OXJN0hj.png', 'name': 'Madhouse', 'origin_country': 'JP'}, {'id': 11884, 'logo_path': '/tHzHPSKdmLT5zCdqFbcmHzZvRIc.png', 'name': 'J.C.STAFF', 'origin_country': 'JP'}, {'id': 528, 'logo_path': '/fO3Aof3lXQclYpBByYC8aneTXwA.png', 'name': 'Bandai Visual', 'origin_country': 'JP'}, {'id': 4720, 'logo_path': '/7wQgZTA4FCkFMDedBYVZiVE87c7.png', 'name': 'ADK', 'origin_country': 'JP'}, {'id': 8157, 'logo_path': '/pEqcMX1aG3JvtDgfh2wNZJLCcb4.png', 'name': 'jeki', 'origin_country': 'JP'}, {'id': 2918, 'logo_path': '/gyEWUBWwqrm3H5T2hkERD9LxpOq.png', 'name': 'Shueisha', 'origin_country': 'JP'}, {'id': 6683, 'logo_path': '/reCkuk3GEomdx5gQpsm15Zl1sgX.png', 'name': 'Lantis', 'origin_country': 'JP'}, {'id': 159157, 'logo_path': '/fEW0xl7S5t0DyFlsx6ypdFC9Poa.png', 'name': 'Banpresto', 'origin_country': 'JP'}, {'id': 60197, 'logo_path': '/D5B8l99tlFJGoMpYJk1kqxYdey.png', 'name': 'Good Smile Company', 'origin_country': 'JP'}, {'id': 3034, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2015-12-04', 'episode_count': 14, 'id': 74239, 'name': 'Specials', 'overview': '', 'poster_path': '/jYUrrLUgZGj1g9VmYa84osrK0zp.jpg', 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2015-10-05', 'episode_count': 12, 'id': 70631, 'name': 'Season 1', 'overview': '', 'poster_path': '/goJyMNld1sDh9WpUyvBpC2cxT2Q.jpg', 'season_number': 1, 'vote_average': 8.2}, {'air_date': '2019-04-10', 'episode_count': 12, 'id': 116885, 'name': 'Season 2', 'overview': 'An increase in villain activity has the Hero Organization worried that the \"Earthdoom prophecy\" will soon come to pass. In an effort to alleviate the overworked heroes, they turn to villains themselves for help. The decision looks ill-advised as at least one villain is more interested in helping the prophecy along than preventing it.', 'poster_path': '/fRibuDuC5MVn19lbvvnuH941w6d.jpg', 'season_number': 2, 'vote_average': 7.6}, {'air_date': None, 'episode_count': 0, 'id': 305144, 'name': 'Season 3', 'overview': '', 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'season_number': 3, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Returning Series', 'tagline': 'All it takes is one punch.', 'type': 'Scripted', 'vote_average': 8.401, 'vote_count': 3747}", "timestamp": "2025-06-29 04:06:51", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 04:06:51", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 04:06:51", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 04:06:56", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含14集", "timestamp": "2025-06-29 04:06:56", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 04:06:56", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 04:06:56", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 04:07:02", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含12集", "timestamp": "2025-06-29 04:07:02", "level": "info"}
{"event": "[季度信息] 正在获取Season 2的详细信息...", "timestamp": "2025-06-29 04:07:02", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 04:07:02", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/2?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 04:07:08", "level": "debug"}
{"event": "[季度信息] 获取Season 2信息成功，包含12集", "timestamp": "2025-06-29 04:07:08", "level": "info"}
{"event": "[季度信息] 正在获取Season 3的详细信息...", "timestamp": "2025-06-29 04:07:08", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 04:07:08", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/3?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 04:07:14", "level": "debug"}
{"event": "[季度信息] 获取Season 3信息成功，包含0集", "timestamp": "2025-06-29 04:07:14", "level": "info"}
{"event": "[季度信息] 电视剧《一拳超人》详细信息获取完成，共4个季度", "timestamp": "2025-06-29 04:07:14", "level": "info"}
{"event": "[视频分析] 分析了 50 个视频文件", "timestamp": "2025-06-29 04:07:38", "level": "info"}
{"event": "Using proactor: IocpProactor", "timestamp": "2025-06-29 13:08:01", "level": "debug"}
{"event": "Invalid value: 0.7", "exc_info": ["<class 'ValueError'>", "ValueError('Invalid value: 0.7')", "<traceback object at 0x0000022AF4359380>"], "timestamp": "2025-06-29 13:08:06", "level": "error"}
{"event": "Invalid value: 0.7", "exc_info": ["<class 'ValueError'>", "ValueError('Invalid value: 0.7')", "<traceback object at 0x0000022AF4337DC0>"], "timestamp": "2025-06-29 13:08:11", "level": "error"}
{"event": "Invalid value: 0.7", "exc_info": ["<class 'ValueError'>", "ValueError('Invalid value: 0.7')", "<traceback object at 0x0000022AF4486600>"], "timestamp": "2025-06-29 13:08:16", "level": "error"}
{"event": "Invalid value: 0.7", "exc_info": ["<class 'ValueError'>", "ValueError('Invalid value: 0.7')", "<traceback object at 0x0000022AF44F3B40>"], "timestamp": "2025-06-29 13:08:16", "level": "error"}
{"event": "Using proactor: IocpProactor", "timestamp": "2025-06-29 13:09:24", "level": "debug"}
{"event": "[配置] 配置已修改为： {'api_key': '844de05e8d21154d1899e21f71c96442', 'bangumi_path': '', 'movie_path': '', 'anime_path': '', 'anime_movie_path': '', 'mode': '链接', 'docker_mnt': '/media', 'ai_api_key': '', 'ai_base_url': 'https://api.openai.com/v1', 'ai_model': 'gpt-4o-mini', 'ai_enabled': False, 'ai_confidence_threshold': 'Medium', 'OPENAI_JSON_MODE': False}", "timestamp": "2025-06-29 13:09:35", "level": "info"}
{"event": "[测试] 强制设置JSON模式为: False", "timestamp": "2025-06-29 13:20:08", "level": "info"}
{"event": "[移除标签工具] One-Punch Man S2", "timestamp": "2025-06-29 13:20:20", "level": "info"}
{"event": "[测试] 处理后的名称: One-Punch Man", "timestamp": "2025-06-29 13:20:20", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:20:20", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=One-Punch+Man&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:20:28", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:20:28", "level": "debug"}
{"event": "[电视剧搜索] 网络错误, 重试第1次中...", "timestamp": "2025-06-29 13:21:05", "level": "warning"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:21:05", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=One-Punch+Man&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:21:13", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:21:13", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:21:21", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 63926, 'adult': False, 'backdrop_path': '/3AXLSxMuqyZt8HyrKKfrcJtkswD.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2015-10-05', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 35, 'name': 'Comedy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': 'https://onepunchman-anime.net/', 'in_production': True, 'languages': ['ja'], 'last_air_date': '2019-07-03', 'last_episode_to_air': {'id': 1732566, 'name': 'Cleaning Up the Disciple’s Mess', 'overview': 'Silverfang and Bomb catch up with Garou while the hero hunter is battling Genos, starting a whirlwind battle that draws in more and more combatants as it marches towards its hair-raising climax!', 'vote_average': 8.2, 'vote_count': 18, 'air_date': '2019-07-03', 'episode_number': 12, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 2, 'show_id': 63926, 'still_path': '/vcZkOnTOa9d5K94DkO604RuRuMU.jpg'}, 'name': 'One-Punch Man', 'next_episode_to_air': None, 'networks': [{'id': 98, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}, {'id': 252, 'logo_path': '/zFZ5KCuvx7K0vP9XgGcidfXjuUd.png', 'name': 'TV Aichi', 'origin_country': 'JP'}, {'id': 824, 'logo_path': '/6foQmoPac7WCDnDuDbuCZklmDuA.png', 'name': 'TV Osaka', 'origin_country': 'JP'}], 'number_of_episodes': 24, 'number_of_seasons': 3, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': 'ワンパンマン', 'overview': \"Saitama is a hero who only became a hero for fun. After three years of “special” training, though, he's become so strong that he's practically invincible. In fact, he's too strong — even his mightiest opponents are taken out with a single punch, and it turns out that being devastatingly powerful is actually kind of a bore. With his passion for being a hero lost along with his hair, yet still faced with new enemies every day, how much longer can he keep it going?\", 'popularity': 47.7579, 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'production_companies': [{'id': 3464, 'logo_path': '/9k0nr75nwnNeT2MHerf1OXJN0hj.png', 'name': 'Madhouse', 'origin_country': 'JP'}, {'id': 11884, 'logo_path': '/tHzHPSKdmLT5zCdqFbcmHzZvRIc.png', 'name': 'J.C.STAFF', 'origin_country': 'JP'}, {'id': 528, 'logo_path': '/fO3Aof3lXQclYpBByYC8aneTXwA.png', 'name': 'Bandai Visual', 'origin_country': 'JP'}, {'id': 4720, 'logo_path': '/7wQgZTA4FCkFMDedBYVZiVE87c7.png', 'name': 'ADK', 'origin_country': 'JP'}, {'id': 8157, 'logo_path': '/pEqcMX1aG3JvtDgfh2wNZJLCcb4.png', 'name': 'jeki', 'origin_country': 'JP'}, {'id': 2918, 'logo_path': '/gyEWUBWwqrm3H5T2hkERD9LxpOq.png', 'name': 'Shueisha', 'origin_country': 'JP'}, {'id': 6683, 'logo_path': '/reCkuk3GEomdx5gQpsm15Zl1sgX.png', 'name': 'Lantis', 'origin_country': 'JP'}, {'id': 159157, 'logo_path': '/fEW0xl7S5t0DyFlsx6ypdFC9Poa.png', 'name': 'Banpresto', 'origin_country': 'JP'}, {'id': 60197, 'logo_path': '/D5B8l99tlFJGoMpYJk1kqxYdey.png', 'name': 'Good Smile Company', 'origin_country': 'JP'}, {'id': 3034, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2015-12-04', 'episode_count': 14, 'id': 74239, 'name': 'Specials', 'overview': '', 'poster_path': '/jYUrrLUgZGj1g9VmYa84osrK0zp.jpg', 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2015-10-05', 'episode_count': 12, 'id': 70631, 'name': 'Season 1', 'overview': '', 'poster_path': '/goJyMNld1sDh9WpUyvBpC2cxT2Q.jpg', 'season_number': 1, 'vote_average': 8.2}, {'air_date': '2019-04-10', 'episode_count': 12, 'id': 116885, 'name': 'Season 2', 'overview': 'An increase in villain activity has the Hero Organization worried that the \"Earthdoom prophecy\" will soon come to pass. In an effort to alleviate the overworked heroes, they turn to villains themselves for help. The decision looks ill-advised as at least one villain is more interested in helping the prophecy along than preventing it.', 'poster_path': '/fRibuDuC5MVn19lbvvnuH941w6d.jpg', 'season_number': 2, 'vote_average': 7.6}, {'air_date': None, 'episode_count': 0, 'id': 305144, 'name': 'Season 3', 'overview': '', 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'season_number': 3, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Returning Series', 'tagline': 'All it takes is one punch.', 'type': 'Scripted', 'vote_average': 8.401, 'vote_count': 3747}", "timestamp": "2025-06-29 13:21:21", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 13:21:21", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:21:21", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:21:29", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含14集", "timestamp": "2025-06-29 13:21:29", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 13:21:29", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:21:29", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:21:37", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含12集", "timestamp": "2025-06-29 13:21:37", "level": "info"}
{"event": "[季度信息] 正在获取Season 2的详细信息...", "timestamp": "2025-06-29 13:21:37", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:21:37", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/2?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:21:46", "level": "debug"}
{"event": "[季度信息] 获取Season 2信息成功，包含12集", "timestamp": "2025-06-29 13:21:46", "level": "info"}
{"event": "[季度信息] 正在获取Season 3的详细信息...", "timestamp": "2025-06-29 13:21:46", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:21:46", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/3?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:21:54", "level": "debug"}
{"event": "[季度信息] 获取Season 3信息成功，包含0集", "timestamp": "2025-06-29 13:21:54", "level": "info"}
{"event": "[季度信息] 电视剧《一拳超人》详细信息获取完成，共4个季度", "timestamp": "2025-06-29 13:21:54", "level": "info"}
{"event": "[视频分析] 分析了 50 个视频文件", "timestamp": "2025-06-29 13:22:19", "level": "info"}
{"event": "[测试] 强制设置JSON模式为: False", "timestamp": "2025-06-29 13:35:53", "level": "info"}
{"event": "[移除标签工具] One-Punch Man S2", "timestamp": "2025-06-29 13:36:04", "level": "info"}
{"event": "[测试] 处理后的名称: One-Punch Man", "timestamp": "2025-06-29 13:36:04", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:36:04", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=One-Punch+Man&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:36:10", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:36:10", "level": "debug"}
{"event": "[测试] 强制设置JSON模式为: False", "timestamp": "2025-06-29 13:37:39", "level": "info"}
{"event": "[移除标签工具] One-Punch Man S2", "timestamp": "2025-06-29 13:37:50", "level": "info"}
{"event": "[测试] 处理后的名称: One-Punch Man", "timestamp": "2025-06-29 13:37:50", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:37:50", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=One-Punch+Man&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:37:55", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:37:55", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:38:01", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 63926, 'adult': False, 'backdrop_path': '/3AXLSxMuqyZt8HyrKKfrcJtkswD.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2015-10-05', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 35, 'name': 'Comedy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': 'https://onepunchman-anime.net/', 'in_production': True, 'languages': ['ja'], 'last_air_date': '2019-07-03', 'last_episode_to_air': {'id': 1732566, 'name': 'Cleaning Up the Disciple’s Mess', 'overview': 'Silverfang and Bomb catch up with Garou while the hero hunter is battling Genos, starting a whirlwind battle that draws in more and more combatants as it marches towards its hair-raising climax!', 'vote_average': 8.2, 'vote_count': 18, 'air_date': '2019-07-03', 'episode_number': 12, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 2, 'show_id': 63926, 'still_path': '/vcZkOnTOa9d5K94DkO604RuRuMU.jpg'}, 'name': 'One-Punch Man', 'next_episode_to_air': None, 'networks': [{'id': 98, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}, {'id': 252, 'logo_path': '/zFZ5KCuvx7K0vP9XgGcidfXjuUd.png', 'name': 'TV Aichi', 'origin_country': 'JP'}, {'id': 824, 'logo_path': '/6foQmoPac7WCDnDuDbuCZklmDuA.png', 'name': 'TV Osaka', 'origin_country': 'JP'}], 'number_of_episodes': 24, 'number_of_seasons': 3, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': 'ワンパンマン', 'overview': \"Saitama is a hero who only became a hero for fun. After three years of “special” training, though, he's become so strong that he's practically invincible. In fact, he's too strong — even his mightiest opponents are taken out with a single punch, and it turns out that being devastatingly powerful is actually kind of a bore. With his passion for being a hero lost along with his hair, yet still faced with new enemies every day, how much longer can he keep it going?\", 'popularity': 47.7579, 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'production_companies': [{'id': 3464, 'logo_path': '/9k0nr75nwnNeT2MHerf1OXJN0hj.png', 'name': 'Madhouse', 'origin_country': 'JP'}, {'id': 11884, 'logo_path': '/tHzHPSKdmLT5zCdqFbcmHzZvRIc.png', 'name': 'J.C.STAFF', 'origin_country': 'JP'}, {'id': 528, 'logo_path': '/fO3Aof3lXQclYpBByYC8aneTXwA.png', 'name': 'Bandai Visual', 'origin_country': 'JP'}, {'id': 4720, 'logo_path': '/7wQgZTA4FCkFMDedBYVZiVE87c7.png', 'name': 'ADK', 'origin_country': 'JP'}, {'id': 8157, 'logo_path': '/pEqcMX1aG3JvtDgfh2wNZJLCcb4.png', 'name': 'jeki', 'origin_country': 'JP'}, {'id': 2918, 'logo_path': '/gyEWUBWwqrm3H5T2hkERD9LxpOq.png', 'name': 'Shueisha', 'origin_country': 'JP'}, {'id': 6683, 'logo_path': '/reCkuk3GEomdx5gQpsm15Zl1sgX.png', 'name': 'Lantis', 'origin_country': 'JP'}, {'id': 159157, 'logo_path': '/fEW0xl7S5t0DyFlsx6ypdFC9Poa.png', 'name': 'Banpresto', 'origin_country': 'JP'}, {'id': 60197, 'logo_path': '/D5B8l99tlFJGoMpYJk1kqxYdey.png', 'name': 'Good Smile Company', 'origin_country': 'JP'}, {'id': 3034, 'logo_path': '/jnuO8pZNEBLEq5YaOP1f5OkmG91.png', 'name': 'TV Tokyo', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2015-12-04', 'episode_count': 14, 'id': 74239, 'name': 'Specials', 'overview': '', 'poster_path': '/jYUrrLUgZGj1g9VmYa84osrK0zp.jpg', 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2015-10-05', 'episode_count': 12, 'id': 70631, 'name': 'Season 1', 'overview': '', 'poster_path': '/goJyMNld1sDh9WpUyvBpC2cxT2Q.jpg', 'season_number': 1, 'vote_average': 8.2}, {'air_date': '2019-04-10', 'episode_count': 12, 'id': 116885, 'name': 'Season 2', 'overview': 'An increase in villain activity has the Hero Organization worried that the \"Earthdoom prophecy\" will soon come to pass. In an effort to alleviate the overworked heroes, they turn to villains themselves for help. The decision looks ill-advised as at least one villain is more interested in helping the prophecy along than preventing it.', 'poster_path': '/fRibuDuC5MVn19lbvvnuH941w6d.jpg', 'season_number': 2, 'vote_average': 7.6}, {'air_date': None, 'episode_count': 0, 'id': 305144, 'name': 'Season 3', 'overview': '', 'poster_path': '/jbYJuxfZMpYDalkiOnBcCv9TaL.jpg', 'season_number': 3, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Returning Series', 'tagline': 'All it takes is one punch.', 'type': 'Scripted', 'vote_average': 8.401, 'vote_count': 3747}", "timestamp": "2025-06-29 13:38:01", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 13:38:01", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:38:01", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:38:07", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含14集", "timestamp": "2025-06-29 13:38:07", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 13:38:07", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:38:07", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:38:13", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含12集", "timestamp": "2025-06-29 13:38:13", "level": "info"}
{"event": "[季度信息] 正在获取Season 2的详细信息...", "timestamp": "2025-06-29 13:38:13", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:38:13", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/2?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:38:18", "level": "debug"}
{"event": "[季度信息] 获取Season 2信息成功，包含12集", "timestamp": "2025-06-29 13:38:18", "level": "info"}
{"event": "[季度信息] 正在获取Season 3的详细信息...", "timestamp": "2025-06-29 13:38:18", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:38:18", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/3?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:38:24", "level": "debug"}
{"event": "[季度信息] 获取Season 3信息成功，包含0集", "timestamp": "2025-06-29 13:38:24", "level": "info"}
{"event": "[季度信息] 电视剧《One-Punch Man》的季度信息填充完成，共4个季度", "timestamp": "2025-06-29 13:38:24", "level": "info"}
{"event": "[视频分析] 分析了 50 个视频文件", "timestamp": "2025-06-29 13:38:50", "level": "info"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 13:38:50", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:38:50", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:38:58", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含14集", "timestamp": "2025-06-29 13:38:58", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 13:38:58", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:38:58", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:39:05", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含12集", "timestamp": "2025-06-29 13:39:06", "level": "info"}
{"event": "[季度信息] 正在获取Season 2的详细信息...", "timestamp": "2025-06-29 13:39:06", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:39:06", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/2?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:39:13", "level": "debug"}
{"event": "[季度信息] 获取Season 2信息成功，包含12集", "timestamp": "2025-06-29 13:39:13", "level": "info"}
{"event": "[季度信息] 正在获取Season 3的详细信息...", "timestamp": "2025-06-29 13:39:13", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:39:13", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/63926/season/3?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:39:21", "level": "debug"}
{"event": "[季度信息] 获取Season 3信息成功，包含0集", "timestamp": "2025-06-29 13:39:21", "level": "info"}
{"event": "[季度信息] 电视剧《One-Punch Man》的季度信息填充完成，共4个季度", "timestamp": "2025-06-29 13:39:21", "level": "info"}
{"event": "[AI识别] 未能从AI响应中提取到任何JSON数据", "timestamp": "2025-06-29 13:47:54", "level": "error"}
{"event": "[测试] 强制设置JSON模式为: False", "timestamp": "2025-06-29 13:49:52", "level": "info"}
{"event": "[移除标签工具] Xam'd Lost Memories", "timestamp": "2025-06-29 13:50:15", "level": "info"}
{"event": "[测试] 处理后的名称: Xam'd Lost Memories", "timestamp": "2025-06-29 13:50:15", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:50:15", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=Xam%27d+Lost+Memories&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:50:21", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:50:21", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:50:27", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 34750, 'adult': False, 'backdrop_path': '/uO3n4bat4Q7NL8gsKvRLyHdm9SJ.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2008-09-24', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 10765, 'name': 'Sci-Fi & Fantasy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': '', 'in_production': False, 'languages': ['ja'], 'last_air_date': '2009-02-04', 'last_episode_to_air': {'id': 791430, 'name': 'The Great Rock and the Girl', 'overview': 'Nakiami seals herself in the Quickening Chamber and the confrontation with the Hiruken Emperor draws to a close. However, Akiyuki turns himself into stone, and nine years later, he awakens and reunite with his loved ones.', 'vote_average': 0.0, 'vote_count': 0, 'air_date': '2009-02-04', 'episode_number': 26, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 1, 'show_id': 34750, 'still_path': '/xYH8mu1TfbZids266LcZVlNFvDx.jpg'}, 'name': \"Xam'd: Lost Memories\", 'next_episode_to_air': None, 'networks': [{'id': 1029, 'logo_path': '/oLrbwkpqM9ShxO67b0HQTgT3tkW.png', 'name': 'PlayStation Network', 'origin_country': ''}], 'number_of_episodes': 26, 'number_of_seasons': 1, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': '亡念のザムド', 'overview': 'Set on a peaceful island during a violent terrorist attack, a young boy is suddenly transformed into a metal-cased mercenary. But with this great power comes even greater danger. Aiyuki must discover how to master this remarkable new power-or risk having this mysterious fusion of rock, metal and magic destroy him!', 'popularity': 38.4364, 'poster_path': '/iK2Nxgs2m7Kj2gvgr1OJ537Qjmw.jpg', 'production_companies': [{'id': 2849, 'logo_path': '/cN3JNIKawjOMtfk0GBVIbsx69bg.png', 'name': 'BONES', 'origin_country': 'JP'}, {'id': 2883, 'logo_path': '/rDYExnBV61jGQnkhVVrPN4Yl7O1.png', 'name': 'Aniplex', 'origin_country': 'JP'}, {'id': 30794, 'logo_path': None, 'name': 'Sony Computer Entertainment', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': None, 'episode_count': 5, 'id': 45516, 'name': 'Specials', 'overview': '', 'poster_path': None, 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2008-09-24', 'episode_count': 26, 'id': 45515, 'name': 'Season 1', 'overview': '', 'poster_path': '/94j9ul9uAECc2qSmlZEYetV2XSt.jpg', 'season_number': 1, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Ended', 'tagline': '', 'type': 'Scripted', 'vote_average': 6.0, 'vote_count': 13}", "timestamp": "2025-06-29 13:50:27", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 13:50:27", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:50:27", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:50:33", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含5集", "timestamp": "2025-06-29 13:50:33", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 13:50:33", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:50:33", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:50:38", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含26集", "timestamp": "2025-06-29 13:50:38", "level": "info"}
{"event": "[季度信息] 电视剧《Xam'd: Lost Memories》的季度信息填充完成，共2个季度", "timestamp": "2025-06-29 13:50:38", "level": "info"}
{"event": "[视频分析] 分析了 83 个视频文件", "timestamp": "2025-06-29 13:51:14", "level": "info"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 13:51:14", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:51:14", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:51:20", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含5集", "timestamp": "2025-06-29 13:51:20", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 13:51:20", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 13:51:20", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 13:51:25", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含26集", "timestamp": "2025-06-29 13:51:25", "level": "info"}
{"event": "[季度信息] 电视剧《Xam'd: Lost Memories》的季度信息填充完成，共2个季度", "timestamp": "2025-06-29 13:51:25", "level": "info"}
{"event": "[AI识别] JSON结构验证失败: 1 validation error for AIAnalysisResult\nfile_mapping.25.episode_type\n  Input should be 'regular', 'special', 'ova' or 'movie' [type=literal_error, input_value='finale', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.10/v/literal_error", "timestamp": "2025-06-29 13:54:00", "level": "error"}
{"event": "[AI识别] 原始数据: {\n  \"confidence\": \"High\",\n  \"reason\": \"本地文件编号从1到26与TMDB的第一季（亡念之扎姆德）的26集数量完全匹配。文件时长与TMDB剧集时长（约24分钟）一致。本地的SPs目录下的文件，如CM、Menu、NCED、NCOP、PV等，属于常见的BD特典，不属于常规剧集。TMDB的第0季有5集，每集时长24分钟，而本地SPs目录下的SP01和SP02时长仅为3-4分钟，与TMDB的第0季剧集不符，因此未进行匹配。\",\n  \"season_mapping\": [\n    {\n      \"local_group_name\": \"Xam'd Lost Memories\",\n      \"maps_to_tmdb_seasons\": [\n        1\n      ]\n    }\n  ],\n  \"file_mapping\": [\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [01][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 1,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [02][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 2,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [03][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 3,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [04][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 4,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [05][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 5,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [06][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 6,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [07][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 7,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [08][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 8,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [09][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 9,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [10][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 10,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [11][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 11,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [12][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 12,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [13][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 13,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [14][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 14,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [15][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 15,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [16][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 16,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [17][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 17,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [18][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 18,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [19][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 19,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [20][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 20,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [21][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 21,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [22][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 22,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [23][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 23,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [24][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 24,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [25][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 25,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [26][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 26,\n      \"episode_type\": \"finale\",\n      \"confidence\": \"High\"\n    }\n  ],\n  \"special_notes\": \"TMDB的第0季（特别篇）包含5集，每集标准时长为24分钟。然而，本地文件SPs目录下的SP01和SP02时长显著短于24分钟，因此无法匹配为TMDB第0季的剧集。SPs目录下其他文件如CM、Menu、NCED、NCOP、PV等均为BD附加内容，并非标准剧集，不应与TMDB的常规剧集或特别篇匹配。\"\n}", "timestamp": "2025-06-29 13:54:00", "level": "error"}
{"event": "[测试] 强制设置JSON模式为: False", "timestamp": "2025-06-29 14:28:24", "level": "info"}
{"event": "[移除标签工具] Xam'd Lost Memories", "timestamp": "2025-06-29 14:28:48", "level": "info"}
{"event": "[测试] 处理后的名称: Xam'd Lost Memories", "timestamp": "2025-06-29 14:28:48", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:28:48", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=Xam%27d+Lost+Memories&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:28:54", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:28:54", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:28:59", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 34750, 'adult': False, 'backdrop_path': '/uO3n4bat4Q7NL8gsKvRLyHdm9SJ.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2008-09-24', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 10765, 'name': 'Sci-Fi & Fantasy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': '', 'in_production': False, 'languages': ['ja'], 'last_air_date': '2009-02-04', 'last_episode_to_air': {'id': 791430, 'name': 'The Great Rock and the Girl', 'overview': 'Nakiami seals herself in the Quickening Chamber and the confrontation with the Hiruken Emperor draws to a close. However, Akiyuki turns himself into stone, and nine years later, he awakens and reunite with his loved ones.', 'vote_average': 0.0, 'vote_count': 0, 'air_date': '2009-02-04', 'episode_number': 26, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 1, 'show_id': 34750, 'still_path': '/xYH8mu1TfbZids266LcZVlNFvDx.jpg'}, 'name': \"Xam'd: Lost Memories\", 'next_episode_to_air': None, 'networks': [{'id': 1029, 'logo_path': '/oLrbwkpqM9ShxO67b0HQTgT3tkW.png', 'name': 'PlayStation Network', 'origin_country': ''}], 'number_of_episodes': 26, 'number_of_seasons': 1, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': '亡念のザムド', 'overview': 'Set on a peaceful island during a violent terrorist attack, a young boy is suddenly transformed into a metal-cased mercenary. But with this great power comes even greater danger. Aiyuki must discover how to master this remarkable new power-or risk having this mysterious fusion of rock, metal and magic destroy him!', 'popularity': 38.4364, 'poster_path': '/iK2Nxgs2m7Kj2gvgr1OJ537Qjmw.jpg', 'production_companies': [{'id': 2849, 'logo_path': '/cN3JNIKawjOMtfk0GBVIbsx69bg.png', 'name': 'BONES', 'origin_country': 'JP'}, {'id': 2883, 'logo_path': '/rDYExnBV61jGQnkhVVrPN4Yl7O1.png', 'name': 'Aniplex', 'origin_country': 'JP'}, {'id': 30794, 'logo_path': None, 'name': 'Sony Computer Entertainment', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': None, 'episode_count': 5, 'id': 45516, 'name': 'Specials', 'overview': '', 'poster_path': None, 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2008-09-24', 'episode_count': 26, 'id': 45515, 'name': 'Season 1', 'overview': '', 'poster_path': '/94j9ul9uAECc2qSmlZEYetV2XSt.jpg', 'season_number': 1, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Ended', 'tagline': '', 'type': 'Scripted', 'vote_average': 6.0, 'vote_count': 13}", "timestamp": "2025-06-29 14:28:59", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 14:28:59", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:28:59", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:29:05", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含5集", "timestamp": "2025-06-29 14:29:05", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 14:29:05", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:29:05", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:29:11", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含26集", "timestamp": "2025-06-29 14:29:11", "level": "info"}
{"event": "[季度信息] 电视剧《Xam'd: Lost Memories》的季度信息填充完成，共2个季度", "timestamp": "2025-06-29 14:29:11", "level": "info"}
{"event": "[视频分析] 分析了 83 个视频文件", "timestamp": "2025-06-29 14:29:44", "level": "info"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 14:29:44", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:29:44", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:29:50", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含5集", "timestamp": "2025-06-29 14:29:50", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 14:29:50", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:29:50", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:29:56", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含26集", "timestamp": "2025-06-29 14:29:56", "level": "info"}
{"event": "[季度信息] 电视剧《Xam'd: Lost Memories》的季度信息填充完成，共2个季度", "timestamp": "2025-06-29 14:29:56", "level": "info"}
{"event": "[AI识别] JSON结构验证失败: 1 validation error for AIAnalysisResult\nfile_mapping.25.episode_type\n  Input should be 'regular', 'special', 'ova' or 'movie' [type=literal_error, input_value='finale', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.10/v/literal_error", "timestamp": "2025-06-29 14:30:38", "level": "error"}
{"event": "[AI识别] 原始数据: {\n  \"confidence\": \"High\",\n  \"reason\": \"本地文件编号从1到26与TMDB的第一季（亡念之扎姆德）的26集数量完全匹配。文件时长与TMDB剧集时长（约24分钟）一致。本地的SPs目录下的文件，如CM、Menu、NCED、NCOP、PV等，属于常见的BD特典，不属于常规剧集。TMDB的第0季有5集，每集时长24分钟，而本地SPs目录下的SP01和SP02时长仅为3-4分钟，与TMDB的第0季剧集不符，因此未进行匹配。\",\n  \"season_mapping\": [\n    {\n      \"local_group_name\": \"Xam'd Lost Memories\",\n      \"maps_to_tmdb_seasons\": [\n        1\n      ]\n    }\n  ],\n  \"file_mapping\": [\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [01][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 1,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [02][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 2,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [03][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 3,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [04][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 4,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [05][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 5,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [06][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 6,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [07][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 7,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [08][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 8,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [09][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 9,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [10][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 10,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [11][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 11,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [12][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 12,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [13][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 13,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [14][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 14,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [15][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 15,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [16][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 16,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [17][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 17,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [18][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 18,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [19][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 19,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [20][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 20,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [21][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 21,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [22][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 22,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [23][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 23,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [24][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 24,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [25][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 25,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[VCB-Studio] Xam'd Lost Memories [26][Ma10p_720p][x265_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 26,\n      \"episode_type\": \"finale\",\n      \"confidence\": \"High\"\n    }\n  ],\n  \"special_notes\": \"TMDB的第0季（特别篇）包含5集，每集标准时长为24分钟。然而，本地文件SPs目录下的SP01和SP02时长显著短于24分钟，因此无法匹配为TMDB第0季的剧集。SPs目录下其他文件如CM、Menu、NCED、NCOP、PV等均为BD附加内容，并非标准剧集，不应与TMDB的常规剧集或特别篇匹配。\"\n}", "timestamp": "2025-06-29 14:30:38", "level": "error"}
{"event": "[测试] 强制设置JSON模式为: False", "timestamp": "2025-06-29 14:36:15", "level": "info"}
{"event": "[移除标签工具] Xam'd Lost Memories", "timestamp": "2025-06-29 14:36:39", "level": "info"}
{"event": "[测试] 处理后的名称: Xam'd Lost Memories", "timestamp": "2025-06-29 14:36:39", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:36:39", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=Xam%27d+Lost+Memories&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:36:44", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:36:44", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:36:50", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 34750, 'adult': False, 'backdrop_path': '/uO3n4bat4Q7NL8gsKvRLyHdm9SJ.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2008-09-24', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 10765, 'name': 'Sci-Fi & Fantasy'}, {'id': 10759, 'name': 'Action & Adventure'}], 'homepage': '', 'in_production': False, 'languages': ['ja'], 'last_air_date': '2009-02-04', 'last_episode_to_air': {'id': 791430, 'name': 'The Great Rock and the Girl', 'overview': 'Nakiami seals herself in the Quickening Chamber and the confrontation with the Hiruken Emperor draws to a close. However, Akiyuki turns himself into stone, and nine years later, he awakens and reunite with his loved ones.', 'vote_average': 0.0, 'vote_count': 0, 'air_date': '2009-02-04', 'episode_number': 26, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 1, 'show_id': 34750, 'still_path': '/xYH8mu1TfbZids266LcZVlNFvDx.jpg'}, 'name': \"Xam'd: Lost Memories\", 'next_episode_to_air': None, 'networks': [{'id': 1029, 'logo_path': '/oLrbwkpqM9ShxO67b0HQTgT3tkW.png', 'name': 'PlayStation Network', 'origin_country': ''}], 'number_of_episodes': 26, 'number_of_seasons': 1, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': '亡念のザムド', 'overview': 'Set on a peaceful island during a violent terrorist attack, a young boy is suddenly transformed into a metal-cased mercenary. But with this great power comes even greater danger. Aiyuki must discover how to master this remarkable new power-or risk having this mysterious fusion of rock, metal and magic destroy him!', 'popularity': 38.4364, 'poster_path': '/iK2Nxgs2m7Kj2gvgr1OJ537Qjmw.jpg', 'production_companies': [{'id': 2849, 'logo_path': '/cN3JNIKawjOMtfk0GBVIbsx69bg.png', 'name': 'BONES', 'origin_country': 'JP'}, {'id': 2883, 'logo_path': '/rDYExnBV61jGQnkhVVrPN4Yl7O1.png', 'name': 'Aniplex', 'origin_country': 'JP'}, {'id': 30794, 'logo_path': None, 'name': 'Sony Computer Entertainment', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': None, 'episode_count': 5, 'id': 45516, 'name': 'Specials', 'overview': '', 'poster_path': None, 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2008-09-24', 'episode_count': 26, 'id': 45515, 'name': 'Season 1', 'overview': '', 'poster_path': '/94j9ul9uAECc2qSmlZEYetV2XSt.jpg', 'season_number': 1, 'vote_average': 0.0}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Ended', 'tagline': '', 'type': 'Scripted', 'vote_average': 6.0, 'vote_count': 13}", "timestamp": "2025-06-29 14:36:50", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 14:36:50", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:36:50", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:36:56", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含5集", "timestamp": "2025-06-29 14:36:56", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 14:36:56", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:36:56", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/34750/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:37:01", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含26集", "timestamp": "2025-06-29 14:37:01", "level": "info"}
{"event": "[季度信息] 电视剧《Xam'd: Lost Memories》的季度信息填充完成，共2个季度", "timestamp": "2025-06-29 14:37:01", "level": "info"}
{"event": "[视频分析] 分析了 83 个视频文件", "timestamp": "2025-06-29 14:37:35", "level": "info"}
{"event": "[AI识别] JSON结构验证成功，置信度: High", "timestamp": "2025-06-29 14:37:49", "level": "info"}
{"event": "[测试] 强制设置JSON模式为: False", "timestamp": "2025-06-29 14:38:24", "level": "info"}
{"event": "[移除标签工具] Chuunibyou demo Koi ga Shitai!", "timestamp": "2025-06-29 14:39:47", "level": "info"}
{"event": "[测试] 处理后的名称: Chuunibyou demo Koi ga Shitai", "timestamp": "2025-06-29 14:39:47", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:39:47", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/search/tv?query=Chuunibyou+demo+Koi+ga+Shitai&language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:39:53", "level": "debug"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:39:53", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/45501?api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:39:58", "level": "debug"}
{"event": "{'base_uri': 'https://api.themoviedb.org/3', 'session': None, 'timeout': None, 'id': 45501, 'adult': False, 'backdrop_path': '/qrxjKSnYymQaG6vohs77WQcyOOL.jpg', 'created_by': [], 'episode_run_time': [24], 'first_air_date': '2012-10-04', 'genres': [{'id': 16, 'name': 'Animation'}, {'id': 35, 'name': 'Comedy'}], 'homepage': 'http://www.anime-chu-2.com/', 'in_production': False, 'languages': ['ja'], 'last_air_date': '2014-03-27', 'last_episode_to_air': {'id': 1026774, 'name': 'The Superior Contract... of Twilight', 'overview': 'Rikka is acting strangely around Yuta, and it takes all of her courage to confess her true feelings to him.', 'vote_average': 9.3, 'vote_count': 3, 'air_date': '2014-03-27', 'episode_number': 12, 'episode_type': 'finale', 'production_code': '', 'runtime': 24, 'season_number': 2, 'show_id': 45501, 'still_path': '/fOmX2jxqCRXO0yz9gPnQGOylosx.jpg'}, 'name': 'Love, Chunibyo & Other Delusions!', 'next_episode_to_air': None, 'networks': [{'id': 614, 'logo_path': '/hSdroyVthq3CynxTIIY7lnS8w1.png', 'name': 'Tokyo MX', 'origin_country': 'JP'}, {'id': 817, 'logo_path': '/wkyNhgg9DDtQMFBGXpAaHilE99N.png', 'name': 'Sun TV', 'origin_country': 'JP'}], 'number_of_episodes': 24, 'number_of_seasons': 2, 'origin_country': ['JP'], 'original_language': 'ja', 'original_name': '中二病でも恋がしたい!', 'overview': 'As one of the thousands of Japanese students afflicted with \"chunibyo,\" a state where they\\'re so desperate to stand out that they\\'ve convinced themselves that they have secret knowledge and hidden powers, Yuta spent most of his middle school years living in a complete fantasy world. He\\'s finally managing to overcome his delusions but his chunibyo have attracted the attentions of another sufferer, and she\\'s decided that this makes him her soul mate.', 'popularity': 32.3107, 'poster_path': '/1IapEqydEiGO0gCgBPVexTFbUCS.jpg', 'production_companies': [{'id': 5438, 'logo_path': '/leZ8GPCL0GneHHayPVnXtB3p0We.png', 'name': 'Kyoto Animation', 'origin_country': 'JP'}, {'id': 9148, 'logo_path': '/rtW3NadfF4kR5mTW00ahiFxw6k7.png', 'name': 'Pony Canyon', 'origin_country': 'JP'}, {'id': 6683, 'logo_path': '/reCkuk3GEomdx5gQpsm15Zl1sgX.png', 'name': 'Lantis', 'origin_country': 'JP'}, {'id': 1393, 'logo_path': '/lUACMATs6jcscXIrzNCQzbvNVN5.png', 'name': 'TBS', 'origin_country': 'JP'}], 'production_countries': [{'iso_3166_1': 'JP', 'name': 'Japan'}], 'seasons': [{'air_date': '2012-09-27', 'episode_count': 34, 'id': 80980, 'name': 'Specials', 'overview': '', 'poster_path': '/iSslPwyNNxzhJRB7vMChdABP6kF.jpg', 'season_number': 0, 'vote_average': 0.0}, {'air_date': '2012-10-04', 'episode_count': 12, 'id': 60484, 'name': 'Love, Chunibyo & Other Delusions!', 'overview': 'Yuta has a problem. He used to be a “chunibyo,” one of the thousands of Japanese students so desperate to stand out that they’ve convinced themselves that they have secret knowledge and hidden powers. Now that he’s starting high school, he’s determined to put aside his delusions. The trouble is that Rikka, his upstairs neighbor, is delusional herself. And she knows all about his past indiscretions. “Crazy in love” is redefined as fantasy worlds collide in Love, Chunibyo & Other Delusions!', 'poster_path': '/2eKpiIE7Qxc3nnCECIQ0eOk7gxe.jpg', 'season_number': 1, 'vote_average': 9.1}, {'air_date': '2014-01-08', 'episode_count': 12, 'id': 60485, 'name': 'Love, Chunibyo & Other Delusions! Heart Throb', 'overview': 'Yuta and Rikka may finally be together, but things haven’t settled down just yet! The arrival of Yuta’s childhood friend, the extra-eccentric Satone Shichimiya, is about to take this couple’s relationship from delusional to downright crazy. Not only is Satone a full-time chunibyo sufferer herself, but now she’s claiming to be Yuta’s one and only soul mate. If Yuta and Rikka want to make their relationship a reality, they’ll have to break through Satone’s fantasies first!', 'poster_path': '/anw0BBKyYQsLtTltDO4xMmpiCW6.jpg', 'season_number': 2, 'vote_average': 9.3}], 'spoken_languages': [{'english_name': 'Japanese', 'iso_639_1': 'ja', 'name': '日本語'}], 'status': 'Ended', 'tagline': '', 'type': 'Scripted', 'vote_average': 8.141, 'vote_count': 391}", "timestamp": "2025-06-29 14:39:58", "level": "debug"}
{"event": "[季度信息] 正在获取Season 0的详细信息...", "timestamp": "2025-06-29 14:39:58", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:39:58", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/45501/season/0?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:40:04", "level": "debug"}
{"event": "[季度信息] 获取Season 0信息成功，包含34集", "timestamp": "2025-06-29 14:40:04", "level": "info"}
{"event": "[季度信息] 正在获取Season 1的详细信息...", "timestamp": "2025-06-29 14:40:04", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:40:04", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/45501/season/1?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:40:10", "level": "debug"}
{"event": "[季度信息] 获取Season 1信息成功，包含12集", "timestamp": "2025-06-29 14:40:10", "level": "info"}
{"event": "[季度信息] 正在获取Season 2的详细信息...", "timestamp": "2025-06-29 14:40:10", "level": "info"}
{"event": "Starting new HTTPS connection (1): api.themoviedb.org:443", "timestamp": "2025-06-29 14:40:10", "level": "debug"}
{"event": "https://api.themoviedb.org:443 \"GET /3/tv/45501/season/2?language=zh-CN&api_key=844de05e8d21154d1899e21f71c96442 HTTP/1.1\" 200 None", "timestamp": "2025-06-29 14:40:16", "level": "debug"}
{"event": "[季度信息] 获取Season 2信息成功，包含12集", "timestamp": "2025-06-29 14:40:16", "level": "info"}
{"event": "[季度信息] 电视剧《Love, Chunibyo & Other Delusions!》的季度信息填充完成，共3个季度", "timestamp": "2025-06-29 14:40:16", "level": "info"}
{"event": "[视频分析] 分析了 156 个视频文件", "timestamp": "2025-06-29 14:41:16", "level": "info"}
{"event": "[AI识别] JSON结构验证失败: 2 validation errors for AIAnalysisResult\nseason_mapping.3.maps_to_tmdb_seasons\n  Value error, maps_to_tmdb_seasons不能为空 [type=value_error, input_value=[], input_type=list]\n    For further information visit https://errors.pydantic.dev/2.10/v/value_error\nova_notes\n  Extra inputs are not permitted [type=extra_forbidden, input_value=\"本地文件包含两部...进行精确匹配。\", input_type=str]\n    For further information visit https://errors.pydantic.dev/2.10/v/extra_forbidden", "timestamp": "2025-06-29 14:47:20", "level": "error"}
{"event": "[AI识别] 原始数据: {\n  \"confidence\": \"High\",\n  \"reason\": \"通过文件名结构、文件夹组织方式和文件时长与TMDB数据中的剧集名称、时长以及集数信息进行比对，确认了TV正片、TV特典、Lite短篇合集与TMDB季度0、季度1、季度2的对应关系。剧场版文件因在TMDB剧集列表中无对应的剧集编号而未被列出，但其相关特典（如光影剧场、被炉系列、拍照时段）均已匹配至TMDB的第0季。\",\n  \"season_mapping\": [\n    {\n      \"local_group_name\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\",\n      \"maps_to_tmdb_seasons\": [\n        1,\n        0\n      ]\n    },\n    {\n      \"local_group_name\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\",\n      \"maps_to_tmdb_seasons\": [\n        2,\n        0\n      ]\n    },\n    {\n      \"local_group_name\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! -Take On Me- [Ma10p_1080p]\",\n      \"maps_to_tmdb_seasons\": [\n        0\n      ]\n    },\n    {\n      \"local_group_name\": \"[Airota&VCB-Studio] Gekijouban Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\",\n      \"maps_to_tmdb_seasons\": []\n    }\n  ],\n  \"file_mapping\": [\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! -Take On Me- [Ma10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! -Take On Me- [SP01][Ma10p_1080p][x265_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 30,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! -Take On Me- [Ma10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! -Take On Me- [SP02][Ma10p_1080p][x265_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 31,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! -Take On Me- [Ma10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! -Take On Me- [SP03][Ma10p_1080p][x265_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 32,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [01][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 1,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [02][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 2,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [03][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 3,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [04][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 4,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [05][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 5,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [06][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 6,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [07][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 7,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [08][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 8,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [09][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 9,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [10][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 10,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [11][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 11,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [12][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 1,\n      \"tmdb_episode\": 12,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [13][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 14,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Lite][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 35,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [SP01][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 7,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [SP02][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 8,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [SP03][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 9,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [SP04][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 10,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [SP05][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 11,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [SP06][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 12,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! [SP07][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 13,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [01][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 1,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [02][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 2,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [03][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 3,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [04][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 4,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [05][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 5,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [06][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 6,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [07][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 7,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [08][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 8,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [09][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 9,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [10][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 10,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [11][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 11,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [12][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 2,\n      \"tmdb_episode\": 12,\n      \"episode_type\": \"regular\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [13][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 29,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Lite][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 36,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [SP01][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 22,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [SP02][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 23,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [SP03][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 24,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [SP04][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 25,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [SP05][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 26,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [SP06][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 27,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    },\n    {\n      \"file_path\": \"[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [Hi10p_1080p]\\\\SPs\\\\[Airota&VCB-Studio] Chuunibyou demo Koi ga Shitai! Ren [SP07][Hi10p_1080p][x264_flac].mkv\",\n      \"tmdb_season\": 0,\n      \"tmdb_episode\": 28,\n      \"episode_type\": \"ova\",\n      \"confidence\": \"High\"\n    }\n  ],\n  \"ova_notes\": \"本地文件包含两部剧场版动画：'Chuunibyou demo Koi ga Shitai! -Take On Me-' (93.7分钟) 和 'Gekijouban Chuunibyou demo Koi ga Shitai!' (96.3分钟)。根据TMDB数据，这两部剧场版本身并未作为剧集直接列在任何季度中（包括第0季），因此未在file_mapping中为它们分配tmdb_season和tmdb_episode。然而，与电影相关的短篇特典如'~决别の…暗黒记念撮影~' ('Take On Me'的三个拍照时段特别影片) 已被成功映射至第0季。此外，两个季度的'Lite'合集（第一季为22.7分钟，TMDB为24分钟；第二季为17.2分钟，TMDB为24分钟）虽然存在时长差异，但鉴于其命名和作为TV Lite合集的普遍性，仍确信其对应关系。S1和S2的BD特典短片（如[IVxx]系列），其时长与TMDB第0季中具体命名的特典集数不符，可能为BD独有或不同形式的打包，因此未进行精确匹配。\"\n}", "timestamp": "2025-06-29 14:47:20", "level": "error"}
