// TMDB 相关类型定义
import { z } from 'zod';
import type {
  AvailableLanguage,
  MovieDetails,
  TvShowDetails,
  SeasonDetails,
  Search,
  MultiSearchResult
} from 'tmdb-ts';

// TMDB 媒体类型
export enum TmdbMediaType {
  MOVIE = 'movie',
  TV = 'tv'
}

// 重新导出 tmdb-ts 中的搜索类型
export type TmdbSearchResultItem = MultiSearchResult;
export type TmdbSearchResponse = Search<MultiSearchResult>;

// 重新导出 tmdb-ts 中的电影详情类型
export type TmdbMovieDetails = MovieDetails;

// 重新导出 tmdb-ts 中的电视剧详情类型
export type TmdbTvDetails = TvShowDetails;

// 重新导出 tmdb-ts 中的季详情类型
export type TmdbSeasonDetails = SeasonDetails;

// 缓存键类型
export interface TmdbCacheKey {
  search: string;
  media: { id: number; type: TmdbMediaType };
  season: { tvId: number; seasonNumber: number };
}

// 错误类型
export class TmdbError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'TmdbError';
  }
}

// TMDB 客户端配置
export interface TmdbClientConfig {
  accessToken: string;
  language?: AvailableLanguage;
  region?: string;
  baseURL?: string;
}
