// 配置管理器
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { parse as parseToml, stringify as stringifyToml } from 'smol-toml';
import { Config, ConfigSchema, DEFAULT_CONFIG } from './types';
import { logger } from '../logger';

export class ConfigManager {
  private static instance: ConfigManager;
  private config: Config;
  private configPath: string;

  private constructor() {
    // 配置文件路径（data 目录下的 seiri.toml）
    this.configPath = join(process.cwd(), 'data', 'seiri.toml');
    this.config = this.loadConfig();
  }

  /**
   * 获取配置管理器单例
   */
  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * 获取当前配置
   */
  public getConfig(): Config {
    return this.config;
  }

  /**
   * 获取通用配置
   */
  public getGeneralConfig() {
    return this.config.general;
  }

  /**
   * 获取 AI 配置
   */
  public getAIConfig() {
    return this.config.ai;
  }

  /**
   * 获取其他配置
   */
  public getOtherConfig() {
    return this.config.other;
  }

  /**
   * 更新配置
   */
  public async updateConfig(newConfig: Partial<Config>): Promise<void> {
    try {
      // 深度合并配置
      const mergedConfig = this.deepMerge(this.config, newConfig);
      
      // 验证配置
      const validatedConfig = ConfigSchema.parse(mergedConfig);
      
      // 保存到文件
      await this.saveConfig(validatedConfig);
      
      // 更新内存中的配置
      this.config = validatedConfig;
      
      logger.info('配置更新成功', { configPath: this.configPath });
    } catch (error) {
      logger.error('配置更新失败', { error, configPath: this.configPath });
      throw new Error(`配置更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 重新加载配置
   */
  public reloadConfig(): Config {
    this.config = this.loadConfig();
    logger.info('配置重新加载成功', { configPath: this.configPath });
    return this.config;
  }

  /**
   * 检查配置文件是否存在
   */
  public configExists(): boolean {
    return existsSync(this.configPath);
  }

  /**
   * 创建默认配置文件
   */
  public async createDefaultConfig(): Promise<void> {
    try {
      await this.saveConfig(DEFAULT_CONFIG);
      this.config = DEFAULT_CONFIG;
      logger.info('默认配置文件创建成功', { configPath: this.configPath });
    } catch (error) {
      logger.error('默认配置文件创建失败', { error, configPath: this.configPath });
      throw new Error(`默认配置文件创建失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 验证配置
   */
  public validateConfig(config: unknown): Config {
    try {
      return ConfigSchema.parse(config);
    } catch (error) {
      logger.error('配置验证失败', { error });
      throw new Error(`配置验证失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从文件加载配置
   */
  private loadConfig(): Config {
    try {
      if (!this.configExists()) {
        logger.warn('配置文件不存在，使用默认配置', { configPath: this.configPath });
        return DEFAULT_CONFIG;
      }

      const configContent = readFileSync(this.configPath, 'utf-8');
      const parsedConfig = parseToml(configContent);
      
      // 与默认配置合并，确保所有字段都存在
      const mergedConfig = this.deepMerge(DEFAULT_CONFIG, parsedConfig);
      
      // 验证配置
      const validatedConfig = ConfigSchema.parse(mergedConfig);
      
      logger.info('配置文件加载成功', { configPath: this.configPath });
      return validatedConfig;
    } catch (error) {
      logger.error('配置文件加载失败，使用默认配置', { 
        error, 
        configPath: this.configPath 
      });
      return DEFAULT_CONFIG;
    }
  }

  /**
   * 保存配置到文件
   */
  private async saveConfig(config: Config): Promise<void> {
    try {
      // 确保配置文件目录存在
      const configDir = dirname(this.configPath);
      if (!existsSync(configDir)) {
        mkdirSync(configDir, { recursive: true });
      }

      const tomlContent = stringifyToml(config);
      writeFileSync(this.configPath, tomlContent, 'utf-8');
    } catch (error) {
      logger.error('配置文件保存失败', { error, configPath: this.configPath });
      throw error;
    }
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
}

// 导出单例实例
export const configManager = ConfigManager.getInstance();
